import { But<PERSON> } from "@/components/ui/button"
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Play, Upload, MessageCircle, Zap } from "lucide-react"
import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-yellow-50 to-red-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-red-500 rounded-full flex items-center justify-center">
              <Play className="w-4 h-4 text-white" />
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-red-600 bg-clip-text text-transparent">
              Aweh.tv
            </h1>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/browse" className="text-gray-600 hover:text-gray-900">
              Browse
            </Link>
            <Link href="/upload" className="text-gray-600 hover:text-gray-900">
              Upload
            </Link>
            <Link href="/stream" className="text-gray-600 hover:text-gray-900">
              Go Live
            </Link>
            <Button>Sign In</Button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-green-600 via-yellow-600 to-red-600 bg-clip-text text-transparent">
            Welcome to Aweh.tv
          </h2>
          <p className="text-xl md:text-2xl text-gray-700 mb-4">The home of South African live streaming</p>
          <p className="text-lg text-gray-600 mb-8">Say it. See it. Stream it.</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-green-600 hover:bg-green-700" asChild>
              <Link href="/stream">
                <Zap className="w-5 h-5 mr-2" />
                Start Streaming
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/browse">
                <Play className="w-5 h-5 mr-2" />
                Watch Streams
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="container mx-auto px-4 py-16">
        <div className="grid md:grid-cols-3 gap-8">
          <Card className="border-green-200 hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Play className="w-6 h-6 text-green-600" />
              </div>
              <CardTitle>Live Streaming</CardTitle>
              <CardDescription>
                Stream live to your audience with high-quality video and real-time interaction
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-yellow-200 hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                <Upload className="w-6 h-6 text-yellow-600" />
              </div>
              <CardTitle>Video Upload</CardTitle>
              <CardDescription>Upload and share your pre-recorded content with the community</CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-red-200 hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <MessageCircle className="w-6 h-6 text-red-600" />
              </div>
              <CardTitle>AI-Powered Chat</CardTitle>
              <CardDescription>Engage with viewers through intelligent chat features and moderation</CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* Stats */}
      <section className="bg-white/50 backdrop-blur-sm py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-green-600 mb-2">1000+</div>
              <div className="text-gray-600">Active Streamers</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-yellow-600 mb-2">50K+</div>
              <div className="text-gray-600">Monthly Viewers</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-red-600 mb-2">24/7</div>
              <div className="text-gray-600">Live Content</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-2xl mx-auto">
          <h3 className="text-3xl font-bold mb-4">Ready to join the community?</h3>
          <p className="text-gray-600 mb-8">
            Start your streaming journey today and connect with viewers across South Africa
          </p>
          <Button
            size="lg"
            className="bg-gradient-to-r from-green-600 to-red-600 hover:from-green-700 hover:to-red-700"
          >
            Get Started Now
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-red-500 rounded-full flex items-center justify-center">
                  <Play className="w-3 h-3 text-white" />
                </div>
                <span className="font-bold">Aweh.tv</span>
              </div>
              <p className="text-gray-400">The home of South African live streaming</p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Platform</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/browse">Browse</Link>
                </li>
                <li>
                  <Link href="/upload">Upload</Link>
                </li>
                <li>
                  <Link href="/stream">Go Live</Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Community</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/creators">Creators</Link>
                </li>
                <li>
                  <Link href="/events">Events</Link>
                </li>
                <li>
                  <Link href="/support">Support</Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Connect</h4>
              <ul className="space-y-2 text-gray-400">
                <li>Twitter</li>
                <li>Instagram</li>
                <li>Facebook</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Aweh.tv. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
