"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { ArrowLeft, Heart, Share, MessageCircle, Send, MoreVertical, Eye, Users, Gift } from "lucide-react"
import Link from "next/link"

export default function WatchPage({ params }: { params: { id: string } }) {
  const [isLiked, setIsLiked] = useState(false)
  const [chatMessage, setChatMessage] = useState("")
  const [showChat, setShowChat] = useState(false)
  const [viewerCount, setViewerCount] = useState(1234)
  const videoRef = useRef<HTMLVideoElement>(null)

  const streamData = {
    id: params.id,
    title: "Gaming with the Boyz - Fortnite Tournament",
    streamer: "GamerSA_Pro",
    viewers: viewerCount,
    likes: 234,
    category: "Gaming",
    thumbnail: "/gaming-stream-thumbnail.png",
    isLive: true,
  }

  const chatMessages = [
    { id: 1, user: "CapeTownFan", message: "Howzit boet! 🇿🇦", timestamp: "12:34" },
    { id: 2, user: "GamerSA", message: "Lekker stream my bru!", timestamp: "12:35" },
    { id: 3, user: "StreamMod", message: "Welcome everyone! Keep it clean 👍", timestamp: "12:36", isMod: true },
    { id: 4, user: "JoziVibes", message: "Eish, that was close!", timestamp: "12:37" },
    { id: 5, user: "DurbanShark", message: "Sharp moves! 🔥", timestamp: "12:38" },
  ]

  useEffect(() => {
    // Simulate viewer count changes
    const interval = setInterval(() => {
      setViewerCount((prev) => prev + Math.floor(Math.random() * 10) - 5)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  const handleSendMessage = () => {
    if (chatMessage.trim()) {
      // Add message logic here
      setChatMessage("")
    }
  }

  const shareStream = () => {
    if (navigator.share) {
      navigator.share({
        title: streamData.title,
        text: `Check out this lekker stream by @${streamData.streamer} on Aweh.tv!`,
        url: window.location.href,
      })
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert("Stream link copied to clipboard!")
    }
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Mobile Layout */}
      <div className="md:hidden">
        {/* Video Player */}
        <div className="relative h-screen w-full">
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            poster={streamData.thumbnail}
            controls={false}
            autoPlay
            muted
          />

          {/* Top Controls */}
          <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/60 to-transparent p-4">
            <div className="flex items-center justify-between">
              <Link href="/browse">
                <ArrowLeft className="w-6 h-6 text-white" />
              </Link>
              <div className="flex items-center space-x-2">
                <Badge className="bg-red-500 text-white">LIVE</Badge>
                <div className="bg-black/50 px-2 py-1 rounded-full flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span className="text-sm">{streamData.viewers.toLocaleString()}</span>
                </div>
              </div>
              <MoreVertical className="w-6 h-6 text-white" />
            </div>
          </div>

          {/* Bottom Info & Actions */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent">
            <div className="p-4">
              <div className="flex items-end justify-between mb-4">
                <div className="flex-1 pr-4">
                  <h1 className="text-white font-bold text-lg mb-1 line-clamp-2">{streamData.title}</h1>
                  <div className="flex items-center space-x-3 mb-2">
                    <p className="text-gray-300 text-sm">@{streamData.streamer}</p>
                    <Button size="sm" className="bg-green-600 hover:bg-green-700 text-xs px-3 py-1">
                      Follow
                    </Button>
                  </div>
                  <Badge variant="outline" className="border-green-500 text-green-400 text-xs">
                    {streamData.category}
                  </Badge>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col items-center space-y-3">
                  <button onClick={() => setIsLiked(!isLiked)} className="flex flex-col items-center space-y-1">
                    <div
                      className={`w-12 h-12 rounded-full flex items-center justify-center ${
                        isLiked ? "bg-red-500" : "bg-white/20"
                      }`}
                    >
                      <Heart className={`w-6 h-6 ${isLiked ? "text-white fill-current" : "text-white"}`} />
                    </div>
                    <span className="text-white text-xs">{streamData.likes + (isLiked ? 1 : 0)}</span>
                  </button>

                  <button onClick={() => setShowChat(!showChat)} className="flex flex-col items-center space-y-1">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                      <MessageCircle className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-white text-xs">Chat</span>
                  </button>

                  <button onClick={shareStream} className="flex flex-col items-center space-y-1">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                      <Share className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-white text-xs">Share</span>
                  </button>

                  <button className="flex flex-col items-center space-y-1">
                    <div className="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center">
                      <Gift className="w-6 h-6 text-white" />
                    </div>
                    <span className="text-white text-xs">Gift</span>
                  </button>
                </div>
              </div>

              {/* Mobile Chat Input */}
              {showChat && (
                <div className="bg-black/60 backdrop-blur-sm rounded-lg p-3 mb-4">
                  <ScrollArea className="h-32 mb-3">
                    <div className="space-y-2">
                      {chatMessages.slice(-5).map((msg) => (
                        <div key={msg.id} className="text-sm">
                          <span className={`font-medium ${msg.isMod ? "text-green-400" : "text-blue-400"}`}>
                            {msg.user}
                          </span>
                          <span className="text-white ml-2">{msg.message}</span>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                  <div className="flex space-x-2">
                    <Input
                      value={chatMessage}
                      onChange={(e) => setChatMessage(e.target.value)}
                      placeholder="Say something lekker..."
                      className="flex-1 bg-gray-800 border-gray-600 text-white text-sm"
                      onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                    />
                    <Button size="sm" onClick={handleSendMessage} className="bg-green-600 hover:bg-green-700">
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden md:block">
        <div className="container mx-auto px-4 py-8">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Main Video */}
            <div className="lg:col-span-3">
              <div className="bg-black rounded-lg overflow-hidden aspect-video mb-4">
                <video ref={videoRef} className="w-full h-full" poster={streamData.thumbnail} controls autoPlay />
              </div>

              <div className="flex items-center justify-between mb-4">
                <div>
                  <h1 className="text-2xl font-bold text-white mb-2">{streamData.title}</h1>
                  <div className="flex items-center space-x-4">
                    <p className="text-gray-400">@{streamData.streamer}</p>
                    <Badge className="bg-red-500">LIVE</Badge>
                    <div className="flex items-center space-x-1 text-gray-400">
                      <Eye className="w-4 h-4" />
                      <span>{streamData.viewers.toLocaleString()} viewers</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    variant={isLiked ? "default" : "outline"}
                    onClick={() => setIsLiked(!isLiked)}
                    className={isLiked ? "bg-red-500 hover:bg-red-600" : ""}
                  >
                    <Heart className={`w-4 h-4 mr-2 ${isLiked ? "fill-current" : ""}`} />
                    {streamData.likes + (isLiked ? 1 : 0)}
                  </Button>
                  <Button variant="outline" onClick={shareStream}>
                    <Share className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                  <Button className="bg-green-600 hover:bg-green-700">Follow</Button>
                </div>
              </div>
            </div>

            {/* Desktop Chat */}
            <div className="lg:col-span-1">
              <div className="bg-gray-900 rounded-lg p-4 h-[600px] flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-bold text-white flex items-center space-x-2">
                    <Users className="w-5 h-5" />
                    <span>Live Chat</span>
                  </h3>
                  <span className="text-sm text-gray-400">{streamData.viewers} viewers</span>
                </div>

                <ScrollArea className="flex-1 mb-4">
                  <div className="space-y-3">
                    {chatMessages.map((msg) => (
                      <div key={msg.id} className="text-sm">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className={`font-medium ${msg.isMod ? "text-green-400" : "text-blue-400"}`}>
                            {msg.user}
                          </span>
                          {msg.isMod && <Badge className="bg-green-600 text-xs">MOD</Badge>}
                          <span className="text-xs text-gray-500">{msg.timestamp}</span>
                        </div>
                        <p className="text-gray-300">{msg.message}</p>
                      </div>
                    ))}
                  </div>
                </ScrollArea>

                <div className="flex space-x-2">
                  <Input
                    value={chatMessage}
                    onChange={(e) => setChatMessage(e.target.value)}
                    placeholder="Say something lekker..."
                    className="flex-1 bg-gray-800 border-gray-600 text-white"
                    onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                  />
                  <Button size="sm" onClick={handleSendMessage} className="bg-green-600 hover:bg-green-700">
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
