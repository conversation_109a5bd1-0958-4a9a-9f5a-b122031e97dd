"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Smartphone, Mic, Camera, Zap, Heart, Volume2, Vibrate } from "lucide-react"

interface Reaction {
  id: string
  emoji: string
  count: number
  isActive: boolean
}

export default function InteractiveMobileFeatures() {
  const [reactions, setReactions] = useState<Reaction[]>([
    { id: "fire", emoji: "🔥", count: 234, isActive: false },
    { id: "heart", emoji: "❤️", count: 189, isActive: false },
    { id: "laugh", emoji: "😂", count: 156, isActive: false },
    { id: "clap", emoji: "👏", count: 98, isActive: false },
    { id: "wow", emoji: "😮", count: 67, isActive: false },
    { id: "flag", emoji: "🇿🇦", count: 145, isActive: false },
  ])

  const [shakeEnabled, setShakeEnabled] = useState(true)
  const [voiceEnabled, setVoiceEnabled] = useState(false)
  const [hapticsEnabled, setHapticsEnabled] = useState(true)
  const [gesturesEnabled, setGesturesEnabled] = useState(true)

  const [isRecording, setIsRecording] = useState(false)
  const [recordingDuration, setRecordingDuration] = useState(0)

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingDuration((prev) => prev + 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isRecording])

  const handleReaction = (reactionId: string) => {
    setReactions((prev) =>
      prev.map((reaction) =>
        reaction.id === reactionId
          ? { ...reaction, count: reaction.count + 1, isActive: true }
          : { ...reaction, isActive: false },
      ),
    )

    // Simulate haptic feedback
    if (hapticsEnabled && "vibrate" in navigator) {
      navigator.vibrate(50)
    }

    // Reset active state after animation
    setTimeout(() => {
      setReactions((prev) => prev.map((reaction) => ({ ...reaction, isActive: false })))
    }, 1000)
  }

  const handleShakeReaction = () => {
    if (shakeEnabled) {
      handleReaction("fire")
      // Simulate shake detection
      alert("Shake detected! 🔥 reaction sent!")
    }
  }

  const startVoiceMessage = () => {
    setIsRecording(true)
    setRecordingDuration(0)
    // Simulate voice recording
    setTimeout(() => {
      setIsRecording(false)
      alert("Voice message sent!")
    }, 3000)
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  return (
    <Card className="bg-gray-900 border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-white">
          <Smartphone className="w-5 h-5" />
          <span>Interactive Features</span>
          <Badge className="bg-green-600">Mobile</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="reactions" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800">
            <TabsTrigger value="reactions">Reactions</TabsTrigger>
            <TabsTrigger value="gestures">Gestures</TabsTrigger>
            <TabsTrigger value="voice">Voice</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="reactions" className="space-y-4">
            {/* Quick Reactions */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Quick Reactions</h4>
              <div className="grid grid-cols-3 gap-3">
                {reactions.map((reaction) => (
                  <Button
                    key={reaction.id}
                    variant="outline"
                    onClick={() => handleReaction(reaction.id)}
                    className={`flex flex-col items-center p-4 h-auto space-y-2 transition-all ${
                      reaction.isActive ? "scale-110 bg-green-600" : ""
                    }`}
                  >
                    <span className="text-2xl">{reaction.emoji}</span>
                    <span className="text-xs text-gray-400">{reaction.count}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Double Tap Info */}
            <div className="p-3 bg-blue-900/20 border border-blue-700 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Heart className="w-4 h-4 text-blue-400" />
                <span className="font-medium text-blue-400">Pro Tip</span>
              </div>
              <p className="text-sm text-gray-300">Double-tap the video to send a quick ❤️ reaction!</p>
            </div>

            {/* Reaction History */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Recent Reactions</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">You reacted with 🔥</span>
                  <span className="text-gray-500">2 min ago</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">You reacted with 🇿🇦</span>
                  <span className="text-gray-500">5 min ago</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">You reacted with ❤️</span>
                  <span className="text-gray-500">8 min ago</span>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="gestures" className="space-y-4">
            {/* Gesture Controls */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Gesture Controls</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Shake to React</span>
                    <p className="text-sm text-gray-400">Shake phone to send 🔥 reaction</p>
                  </div>
                  <Button
                    size="sm"
                    variant={shakeEnabled ? "default" : "outline"}
                    onClick={() => setShakeEnabled(!shakeEnabled)}
                  >
                    {shakeEnabled ? "On" : "Off"}
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Swipe Navigation</span>
                    <p className="text-sm text-gray-400">Swipe up/down to browse streams</p>
                  </div>
                  <Button
                    size="sm"
                    variant={gesturesEnabled ? "default" : "outline"}
                    onClick={() => setGesturesEnabled(!gesturesEnabled)}
                  >
                    {gesturesEnabled ? "On" : "Off"}
                  </Button>
                </div>
              </div>
            </div>

            {/* Test Gestures */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Test Gestures</h4>
              <div className="space-y-2">
                <Button onClick={handleShakeReaction} className="w-full bg-transparent" variant="outline">
                  <Zap className="w-4 h-4 mr-2" />
                  Simulate Shake
                </Button>
                <Button className="w-full bg-transparent" variant="outline">
                  <Camera className="w-4 h-4 mr-2" />
                  Test Double Tap
                </Button>
              </div>
            </div>

            {/* Gesture Guide */}
            <div className="p-3 bg-green-900/20 border border-green-700 rounded-lg">
              <h4 className="font-medium text-green-400 mb-2">Gesture Guide</h4>
              <div className="space-y-1 text-sm text-gray-300">
                <p>• Double tap: Quick like ❤️</p>
                <p>• Shake: Fire reaction 🔥</p>
                <p>• Swipe up: Next stream</p>
                <p>• Swipe down: Previous stream</p>
                <p>• Long press: More options</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="voice" className="space-y-4">
            {/* Voice Messages */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Voice Messages</h4>
              {isRecording ? (
                <div className="text-center py-6">
                  <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-3 animate-pulse">
                    <Mic className="w-8 h-8 text-white" />
                  </div>
                  <p className="text-white font-medium">Recording...</p>
                  <p className="text-gray-400 text-sm">{formatDuration(recordingDuration)}</p>
                  <Button onClick={() => setIsRecording(false)} className="mt-3 bg-red-600 hover:bg-red-700" size="sm">
                    Stop Recording
                  </Button>
                </div>
              ) : (
                <div className="text-center py-6">
                  <Button
                    onClick={startVoiceMessage}
                    disabled={!voiceEnabled}
                    className="w-16 h-16 bg-green-600 hover:bg-green-700 rounded-full p-0"
                  >
                    <Mic className="w-8 h-8" />
                  </Button>
                  <p className="text-white mt-2">Hold to record voice message</p>
                  <p className="text-gray-400 text-sm">Max 30 seconds</p>
                </div>
              )}
            </div>

            {/* Voice Settings */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Voice Settings</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Voice Messages</span>
                    <p className="text-sm text-gray-400">Send voice messages in chat</p>
                  </div>
                  <Button
                    size="sm"
                    variant={voiceEnabled ? "default" : "outline"}
                    onClick={() => setVoiceEnabled(!voiceEnabled)}
                  >
                    {voiceEnabled ? "On" : "Off"}
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Auto-transcribe</span>
                    <p className="text-sm text-gray-400">Convert voice to text automatically</p>
                  </div>
                  <Button size="sm" variant="outline">
                    On
                  </Button>
                </div>
              </div>
            </div>

            {/* Recent Voice Messages */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Recent Voice Messages</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-3 p-2 bg-gray-700 rounded">
                  <Volume2 className="w-4 h-4 text-blue-400" />
                  <div className="flex-1">
                    <p className="text-sm text-white">"Howzit boet, lekker stream!"</p>
                    <p className="text-xs text-gray-400">2 min ago • 0:03</p>
                  </div>
                  <Button size="sm" variant="ghost">
                    Play
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            {/* Haptic Feedback */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Haptic Feedback</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Vibrate className="w-4 h-4 text-purple-400" />
                    <div>
                      <span className="text-white">Vibration</span>
                      <p className="text-sm text-gray-400">Vibrate on reactions and interactions</p>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant={hapticsEnabled ? "default" : "outline"}
                    onClick={() => setHapticsEnabled(!hapticsEnabled)}
                  >
                    {hapticsEnabled ? "On" : "Off"}
                  </Button>
                </div>
                <Button
                  onClick={() => navigator.vibrate && navigator.vibrate(100)}
                  className="w-full"
                  variant="outline"
                  size="sm"
                >
                  Test Vibration
                </Button>
              </div>
            </div>

            {/* Accessibility */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Accessibility</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Large Touch Targets</span>
                    <p className="text-sm text-gray-400">Bigger buttons for easier tapping</p>
                  </div>
                  <Button size="sm" variant="outline">
                    On
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Voice Commands</span>
                    <p className="text-sm text-gray-400">Control app with voice</p>
                  </div>
                  <Button size="sm" variant="outline">
                    Off
                  </Button>
                </div>
              </div>
            </div>

            {/* Performance */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Performance</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Reduce Animations</span>
                    <p className="text-sm text-gray-400">Improve performance on older devices</p>
                  </div>
                  <Button size="sm" variant="outline">
                    Off
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Battery Saver</span>
                    <p className="text-sm text-gray-400">Optimize for battery life</p>
                  </div>
                  <Button size="sm" variant="outline">
                    Auto
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
