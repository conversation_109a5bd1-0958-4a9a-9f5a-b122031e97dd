"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Users, Gift, Trophy, Target, Zap, Heart, Star, Crown } from "lucide-react"

interface Creator {
  id: string
  name: string
  avatar: string
  followers: number
  earnings: number
  category: string
  isVerified: boolean
}

interface Challenge {
  id: string
  title: string
  description: string
  prize: number
  participants: number
  endDate: string
  category: string
}

export default function CommunityMonetization() {
  const [selectedTier, setSelectedTier] = useState("bronze")
  const [tipAmount, setTipAmount] = useState(25)
  const [customAmount, setCustomAmount] = useState("")

  const creatorTiers = [
    {
      id: "bronze",
      name: "Bronze Creator",
      icon: "🥉",
      requirements: "100+ followers",
      benefits: ["Basic analytics", "Custom emotes", "Priority support"],
      color: "bg-orange-600",
    },
    {
      id: "silver",
      name: "Silver Creator",
      icon: "🥈",
      requirements: "1K+ followers",
      benefits: ["Advanced analytics", "Subscriber perks", "Revenue sharing"],
      color: "bg-gray-400",
    },
    {
      id: "gold",
      name: "Gold Creator",
      icon: "🥇",
      requirements: "10K+ followers",
      benefits: ["Premium features", "Brand partnerships", "Exclusive events"],
      color: "bg-yellow-500",
    },
    {
      id: "diamond",
      name: "Diamond Creator",
      icon: "💎",
      requirements: "100K+ followers",
      benefits: ["All features", "Personal manager", "Revenue guarantee"],
      color: "bg-blue-500",
    },
  ]

  const saTips = [
    { amount: 10, label: "Coffee", emoji: "☕", description: "Buy me a coffee", popular: false },
    { amount: 25, label: "Boerewors", emoji: "🌭", description: "Boerewors roll fund", popular: true },
    { amount: 50, label: "Braai", emoji: "🔥", description: "Braai contribution", popular: true },
    { amount: 100, label: "Biltong", emoji: "🥩", description: "Biltong money", popular: false },
    { amount: 200, label: "Lekker", emoji: "🎉", description: "That was lekker!", popular: false },
    { amount: 500, label: "Legend", emoji: "👑", description: "You're a legend!", popular: false },
  ]

  const challenges = [
    {
      id: "1",
      title: "Heritage Day Challenge",
      description: "Show off your cultural heritage",
      prize: 5000,
      participants: 234,
      endDate: "24 Sept",
      category: "Culture",
    },
    {
      id: "2",
      title: "Best Braai Master",
      description: "Ultimate braai cooking competition",
      prize: 2500,
      participants: 156,
      endDate: "30 Sept",
      category: "Cooking",
    },
    {
      id: "3",
      title: "SA Gaming Tournament",
      description: "Fortnite tournament for SA players",
      prize: 10000,
      participants: 567,
      endDate: "15 Oct",
      category: "Gaming",
    },
  ]

  const topCreators = [
    {
      id: "1",
      name: "BraaiMaster_GP",
      avatar: "/placeholder.svg?height=40&width=40&text=BM",
      followers: 45600,
      earnings: 12500,
      category: "Cooking",
      isVerified: true,
    },
    {
      id: "2",
      name: "CapeTownVibes",
      avatar: "/placeholder.svg?height=40&width=40&text=CV",
      followers: 38200,
      earnings: 9800,
      category: "Travel",
      isVerified: true,
    },
    {
      id: "3",
      name: "GamerSA_Pro",
      avatar: "/placeholder.svg?height=40&width=40&text=GP",
      followers: 52100,
      earnings: 15600,
      category: "Gaming",
      isVerified: true,
    },
  ]

  return (
    <div className="space-y-6">
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <Users className="w-5 h-5" />
            <span>Community & Monetization</span>
            <Badge className="bg-green-600">SA Local</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="tipping" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-gray-800">
              <TabsTrigger value="tipping">SA Tipping</TabsTrigger>
              <TabsTrigger value="creators">Creators</TabsTrigger>
              <TabsTrigger value="challenges">Challenges</TabsTrigger>
              <TabsTrigger value="community">Community</TabsTrigger>
            </TabsList>

            <TabsContent value="tipping" className="space-y-4">
              {/* SA-Style Tipping */}
              <div className="p-4 bg-gray-800 rounded-lg">
                <h4 className="font-medium text-white mb-3">Support with SA Style 🇿🇦</h4>
                <div className="grid grid-cols-2 gap-3">
                  {saTips.map((tip) => (
                    <Button
                      key={tip.amount}
                      variant={tipAmount === tip.amount ? "default" : "outline"}
                      onClick={() => {
                        setTipAmount(tip.amount)
                        setCustomAmount("")
                      }}
                      className="flex flex-col items-center p-4 h-auto space-y-2 relative"
                    >
                      {tip.popular && <Badge className="absolute -top-2 -right-2 bg-green-600 text-xs">Popular</Badge>}
                      <span className="text-2xl">{tip.emoji}</span>
                      <span className="text-sm font-medium">R{tip.amount}</span>
                      <span className="text-xs text-gray-400 text-center">{tip.description}</span>
                    </Button>
                  ))}
                </div>
              </div>

              {/* Custom Amount */}
              <div className="p-4 bg-gray-800 rounded-lg">
                <h4 className="font-medium text-white mb-3">Custom Amount</h4>
                <div className="flex space-x-2">
                  <Input
                    type="number"
                    placeholder="Enter amount (ZAR)"
                    value={customAmount}
                    onChange={(e) => setCustomAmount(e.target.value)}
                    className="bg-gray-700 border-gray-600 text-white"
                  />
                  <Button className="bg-green-600 hover:bg-green-700">
                    <Gift className="w-4 h-4 mr-2" />
                    Tip R{customAmount || tipAmount}
                  </Button>
                </div>
              </div>

              {/* Payment Methods */}
              <div className="p-4 bg-gray-800 rounded-lg">
                <h4 className="font-medium text-white mb-3">Payment Methods</h4>
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline" size="sm" className="justify-start bg-transparent">
                    📱 SnapScan
                  </Button>
                  <Button variant="outline" size="sm" className="justify-start bg-transparent">
                    ⚡ Zapper
                  </Button>
                  <Button variant="outline" size="sm" className="justify-start bg-transparent">
                    🏦 Capitec Pay
                  </Button>
                  <Button variant="outline" size="sm" className="justify-start bg-transparent">
                    📞 Airtime
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="creators" className="space-y-4">
              {/* Creator Tiers */}
              <div className="p-4 bg-gray-800 rounded-lg">
                <h4 className="font-medium text-white mb-3">Creator Program</h4>
                <div className="space-y-3">
                  {creatorTiers.map((tier) => (
                    <div
                      key={tier.id}
                      className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                        selectedTier === tier.id ? "border-green-500 bg-green-900/20" : "border-gray-600 bg-gray-700"
                      }`}
                      onClick={() => setSelectedTier(tier.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-2xl">{tier.icon}</span>
                          <span className="font-medium text-white">{tier.name}</span>
                        </div>
                        <Badge className={tier.color}>{tier.requirements}</Badge>
                      </div>
                      <div className="text-sm text-gray-300">
                        {tier.benefits.map((benefit, index) => (
                          <span key={index}>
                            • {benefit}
                            {index < tier.benefits.length - 1 ? " " : ""}
                          </span>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Top Creators */}
              <div className="p-4 bg-gray-800 rounded-lg">
                <h4 className="font-medium text-white mb-3">Top SA Creators</h4>
                <div className="space-y-3">
                  {topCreators.map((creator, index) => (
                    <div key={creator.id} className="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-yellow-400">#{index + 1}</span>
                        <img
                          src={creator.avatar || "/placeholder.svg"}
                          alt={creator.name}
                          className="w-10 h-10 rounded-full"
                        />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-white">{creator.name}</span>
                          {creator.isVerified && <Badge className="bg-blue-600">✓</Badge>}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          <span>{creator.followers.toLocaleString()} followers</span>
                          <span>R{creator.earnings.toLocaleString()} earned</span>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {creator.category}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="challenges" className="space-y-4">
              {/* Active Challenges */}
              <div className="space-y-3">
                {challenges.map((challenge) => (
                  <div key={challenge.id} className="p-4 bg-gray-800 rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <Trophy className="w-5 h-5 text-yellow-400" />
                          <h4 className="font-bold text-white">{challenge.title}</h4>
                          <Badge className="bg-green-600">R{challenge.prize.toLocaleString()}</Badge>
                        </div>
                        <p className="text-sm text-gray-400 mb-2">{challenge.description}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span>{challenge.participants} participants</span>
                          <span>Ends {challenge.endDate}</span>
                          <Badge variant="outline" className="text-xs">
                            {challenge.category}
                          </Badge>
                        </div>
                      </div>
                      <Button size="sm" className="bg-green-600 hover:bg-green-700">
                        Join Challenge
                      </Button>
                    </div>
                    <Progress value={Math.random() * 100} className="w-full" />
                  </div>
                ))}
              </div>

              {/* Create Challenge */}
              <div className="p-4 bg-gray-800 rounded-lg">
                <h4 className="font-medium text-white mb-3">Create Your Challenge</h4>
                <p className="text-sm text-gray-400 mb-3">
                  Start a community challenge and set prizes for participants
                </p>
                <Button className="w-full bg-purple-600 hover:bg-purple-700">
                  <Target className="w-4 h-4 mr-2" />
                  Create Challenge
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="community" className="space-y-4">
              {/* Community Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-gray-800 rounded-lg text-center">
                  <Users className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-white">125K</div>
                  <div className="text-sm text-gray-400">Active Users</div>
                </div>
                <div className="p-4 bg-gray-800 rounded-lg text-center">
                  <Zap className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-white">2.3M</div>
                  <div className="text-sm text-gray-400">Tips Sent</div>
                </div>
              </div>

              {/* Community Features */}
              <div className="p-4 bg-gray-800 rounded-lg">
                <h4 className="font-medium text-white mb-3">Community Features</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Heart className="w-4 h-4 text-red-400" />
                      <span className="text-white">Creator Support Groups</span>
                    </div>
                    <Badge className="bg-green-600">Active</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Star className="w-4 h-4 text-yellow-400" />
                      <span className="text-white">Monthly Creator Awards</span>
                    </div>
                    <Badge className="bg-green-600">Active</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Crown className="w-4 h-4 text-purple-400" />
                      <span className="text-white">VIP Creator Program</span>
                    </div>
                    <Badge variant="outline">Coming Soon</Badge>
                  </div>
                </div>
              </div>

              {/* Leaderboards */}
              <div className="p-4 bg-gray-800 rounded-lg">
                <h4 className="font-medium text-white mb-3">This Month's Leaders</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-2 bg-yellow-900/20 rounded">
                    <div className="flex items-center space-x-2">
                      <span className="text-yellow-400 font-bold">🥇</span>
                      <span className="text-white">Most Tips Received</span>
                    </div>
                    <span className="text-yellow-400">BraaiMaster_GP</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-gray-700 rounded">
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-400 font-bold">🥈</span>
                      <span className="text-white">Most Active Streamer</span>
                    </div>
                    <span className="text-gray-300">CapeTownVibes</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-orange-900/20 rounded">
                    <div className="flex items-center space-x-2">
                      <span className="text-orange-400 font-bold">🥉</span>
                      <span className="text-white">Rising Star</span>
                    </div>
                    <span className="text-orange-400">NewCreator_KZN</span>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
