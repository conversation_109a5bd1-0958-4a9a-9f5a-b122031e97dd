"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Settings, Edit, Play, Eye, Users, Calendar, MapPin, LinkIcon, Share } from "lucide-react"
import Link from "next/link"

const userProfile = {
  username: "SAStreamer_Pro",
  displayName: "Thabo Mthembu",
  bio: "Gaming enthusiast from Joburg 🇿🇦 | Streaming daily | Lekker vibes only!",
  location: "Johannesburg, South Africa",
  joinDate: "March 2023",
  website: "https://sastreamer.co.za",
  avatar: "/placeholder.svg?height=100&width=100&text=TM",
  followers: 12500,
  following: 234,
  totalViews: 1250000,
  totalStreams: 156,
}

const userVideos = [
  {
    id: 1,
    title: "Epic Fortnite Victory Royale",
    views: 15420,
    duration: "12:34",
    thumbnail: "/gaming-stream-thumbnail.png",
    uploadedAt: "2 days ago",
  },
  {
    id: 2,
    title: "Reacting to SA Gaming News",
    views: 8765,
    duration: "18:45",
    thumbnail: "/placeholder.svg?height=200&width=300&text=Gaming+News",
    uploadedAt: "1 week ago",
  },
  {
    id: 3,
    title: "Chatting with Viewers - Q&A",
    views: 23100,
    duration: "45:22",
    thumbnail: "/placeholder.svg?height=200&width=300&text=Q%26A+Stream",
    uploadedAt: "2 weeks ago",
  },
]

export default function ProfilePage() {
  const [isFollowing, setIsFollowing] = useState(false)
  const [isOwnProfile] = useState(true) // In real app, check if viewing own profile

  return (
    <div className="min-h-screen bg-black text-white pb-20">
      {/* Header */}
      <div className="sticky top-0 z-50 bg-black/90 backdrop-blur-sm border-b border-gray-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold">Profile</h1>
            <div className="flex items-center space-x-2">
              <Button size="sm" variant="ghost">
                <Share className="w-4 h-4" />
              </Button>
              {isOwnProfile && (
                <Link href="/settings">
                  <Button size="sm" variant="ghost">
                    <Settings className="w-4 h-4" />
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {/* Profile Header */}
        <div className="text-center mb-8">
          <Avatar className="w-24 h-24 mx-auto mb-4 border-2 border-green-500">
            <AvatarImage src={userProfile.avatar || "/placeholder.svg"} alt={userProfile.displayName} />
            <AvatarFallback className="bg-green-600 text-white text-2xl">
              {userProfile.displayName
                .split(" ")
                .map((n) => n[0])
                .join("")}
            </AvatarFallback>
          </Avatar>

          <h2 className="text-2xl font-bold mb-1">{userProfile.displayName}</h2>
          <p className="text-gray-400 mb-3">@{userProfile.username}</p>

          <p className="text-gray-300 mb-4 max-w-md mx-auto">{userProfile.bio}</p>

          <div className="flex items-center justify-center space-x-4 text-sm text-gray-400 mb-4">
            <div className="flex items-center space-x-1">
              <MapPin className="w-4 h-4" />
              <span>{userProfile.location}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <span>Joined {userProfile.joinDate}</span>
            </div>
          </div>

          {userProfile.website && (
            <div className="flex items-center justify-center space-x-1 text-sm text-green-400 mb-4">
              <LinkIcon className="w-4 h-4" />
              <a href={userProfile.website} className="hover:underline">
                {userProfile.website.replace("https://", "")}
              </a>
            </div>
          )}

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{userProfile.followers.toLocaleString()}</div>
              <div className="text-sm text-gray-400">Followers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{userProfile.following.toLocaleString()}</div>
              <div className="text-sm text-gray-400">Following</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">{userProfile.totalViews.toLocaleString()}</div>
              <div className="text-sm text-gray-400">Total Views</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-400">{userProfile.totalStreams}</div>
              <div className="text-sm text-gray-400">Streams</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-center space-x-3">
            {isOwnProfile ? (
              <>
                <Button className="bg-green-600 hover:bg-green-700">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Profile
                </Button>
                <Link href="/stream">
                  <Button variant="outline" className="border-green-500 text-green-400 bg-transparent">
                    <Play className="w-4 h-4 mr-2" />
                    Go Live
                  </Button>
                </Link>
              </>
            ) : (
              <>
                <Button
                  onClick={() => setIsFollowing(!isFollowing)}
                  className={isFollowing ? "bg-gray-600 hover:bg-gray-700" : "bg-green-600 hover:bg-green-700"}
                >
                  <Users className="w-4 h-4 mr-2" />
                  {isFollowing ? "Following" : "Follow"}
                </Button>
                <Button variant="outline" className="border-gray-600 text-gray-300 bg-transparent">
                  Message
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Content Tabs */}
        <Tabs defaultValue="videos" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-gray-900">
            <TabsTrigger value="videos" className="data-[state=active]:bg-green-600">
              Videos
            </TabsTrigger>
            <TabsTrigger value="streams" className="data-[state=active]:bg-green-600">
              Live Streams
            </TabsTrigger>
            <TabsTrigger value="about" className="data-[state=active]:bg-green-600">
              About
            </TabsTrigger>
          </TabsList>

          <TabsContent value="videos" className="mt-6">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {userVideos.map((video) => (
                <Card
                  key={video.id}
                  className="bg-gray-900 border-gray-700 overflow-hidden hover:shadow-lg transition-shadow"
                >
                  <div className="relative">
                    <img
                      src={video.thumbnail || "/placeholder.svg"}
                      alt={video.title}
                      className="w-full h-32 md:h-40 object-cover"
                    />
                    <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                      {video.duration}
                    </div>
                    <Link
                      href={`/watch/${video.id}`}
                      className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/50 transition-opacity"
                    >
                      <Play className="w-8 h-8 text-white" />
                    </Link>
                  </div>
                  <CardContent className="p-3">
                    <h3 className="font-semibold text-sm line-clamp-2 text-white mb-2">{video.title}</h3>
                    <div className="flex items-center justify-between text-xs text-gray-400">
                      <span className="flex items-center space-x-1">
                        <Eye className="w-3 h-3" />
                        <span>{video.views.toLocaleString()}</span>
                      </span>
                      <span>{video.uploadedAt}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="streams" className="mt-6">
            <div className="text-center py-12">
              <Play className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">No recent streams</h3>
              <p className="text-gray-500 mb-6">Start streaming to see your live content here</p>
              {isOwnProfile && (
                <Link href="/stream">
                  <Button className="bg-green-600 hover:bg-green-700">
                    <Play className="w-4 h-4 mr-2" />
                    Go Live Now
                  </Button>
                </Link>
              )}
            </div>
          </TabsContent>

          <TabsContent value="about" className="mt-6">
            <Card className="bg-gray-900 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">About {userProfile.displayName}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold text-white mb-2">Bio</h4>
                  <p className="text-gray-300">{userProfile.bio}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-2">Streaming Since</h4>
                  <p className="text-gray-300">{userProfile.joinDate}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-2">Location</h4>
                  <p className="text-gray-300">{userProfile.location}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-2">Achievements</h4>
                  <div className="flex flex-wrap gap-2">
                    <Badge className="bg-yellow-600">10K+ Followers</Badge>
                    <Badge className="bg-purple-600">1M+ Views</Badge>
                    <Badge className="bg-green-600">Verified Creator</Badge>
                    <Badge className="bg-red-600">SA Local</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
