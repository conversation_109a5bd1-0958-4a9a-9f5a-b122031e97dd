"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Heart, Play, Eye, Clock, Trash2 } from "lucide-react"
import Link from "next/link"

const likedVideos = [
  {
    id: 1,
    title: "Best Braai Tips for Summer",
    creator: "BraaiMaster",
    views: 15420,
    duration: "12:34",
    thumbnail: "/south-african-braai-tips.png",
    likedAt: "2 days ago",
    category: "Cooking",
  },
  {
    id: 2,
    title: "Cape Town Sunset Vibes",
    creator: "CapeTownVibes",
    views: 8765,
    duration: "18:45",
    thumbnail: "/cape-town-sunset.png",
    likedAt: "1 week ago",
    category: "Travel",
  },
  {
    id: 3,
    title: "Gaming with the Boyz - Fortnite Tournament",
    creator: "GamerSA_Pro",
    views: 23100,
    duration: "45:22",
    thumbnail: "/gaming-stream-thumbnail.png",
    likedAt: "3 days ago",
    category: "Gaming",
  },
  {
    id: 4,
    title: "Traditional Zulu Dance Performance",
    creator: "CulturalSA",
    views: 12300,
    duration: "8:22",
    thumbnail: "/zulu-dance.png",
    likedAt: "5 days ago",
    category: "Culture",
  },
]

export default function FavoritesPage() {
  const [favorites, setFavorites] = useState(likedVideos)

  const removeFavorite = (id: number) => {
    setFavorites(favorites.filter((video) => video.id !== id))
  }

  return (
    <div className="min-h-screen bg-black text-white pb-20">
      {/* Header */}
      <div className="sticky top-0 z-50 bg-black/90 backdrop-blur-sm border-b border-gray-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-3">
            <Heart className="w-6 h-6 text-red-500" />
            <h1 className="text-2xl font-bold">Liked Videos</h1>
            <Badge variant="outline" className="border-red-500 text-red-400">
              {favorites.length}
            </Badge>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        {favorites.length === 0 ? (
          <div className="text-center py-20">
            <Heart className="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-400 mb-2">No liked videos yet</h2>
            <p className="text-gray-500 mb-6">Start exploring and like videos you enjoy!</p>
            <Link href="/browse">
              <Button className="bg-green-600 hover:bg-green-700">Browse Videos</Button>
            </Link>
          </div>
        ) : (
          <>
            {/* Mobile View */}
            <div className="md:hidden space-y-4">
              {favorites.map((video) => (
                <Card key={video.id} className="bg-gray-900 border-gray-700 overflow-hidden">
                  <div className="flex">
                    <div className="relative w-32 h-20 flex-shrink-0">
                      <img
                        src={video.thumbnail || "/placeholder.svg"}
                        alt={video.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute bottom-1 right-1 bg-black/70 text-white px-1 py-0.5 rounded text-xs flex items-center space-x-1">
                        <Clock className="w-2 h-2" />
                        <span>{video.duration}</span>
                      </div>
                      <Link href={`/watch/${video.id}`} className="absolute inset-0 flex items-center justify-center">
                        <Play className="w-6 h-6 text-white" />
                      </Link>
                    </div>
                    <CardContent className="flex-1 p-3">
                      <h3 className="font-semibold text-white text-sm line-clamp-2 mb-1">{video.title}</h3>
                      <p className="text-xs text-gray-400 mb-1">{video.creator}</p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <span className="flex items-center space-x-1">
                            <Eye className="w-3 h-3" />
                            <span>{video.views.toLocaleString()}</span>
                          </span>
                          <span>•</span>
                          <span>{video.likedAt}</span>
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeFavorite(video.id)}
                          className="text-red-400 hover:text-red-300 p-1"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                      <Badge variant="outline" className="border-green-500 text-green-400 text-xs mt-2">
                        {video.category}
                      </Badge>
                    </CardContent>
                  </div>
                </Card>
              ))}
            </div>

            {/* Desktop View */}
            <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {favorites.map((video) => (
                <Card
                  key={video.id}
                  className="bg-gray-900 border-gray-700 overflow-hidden hover:shadow-lg transition-shadow"
                >
                  <div className="relative">
                    <img
                      src={video.thumbnail || "/placeholder.svg"}
                      alt={video.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute bottom-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{video.duration}</span>
                    </div>
                    <Link
                      href={`/watch/${video.id}`}
                      className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/50 transition-opacity"
                    >
                      <Play className="w-12 h-12 text-white" />
                    </Link>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeFavorite(video.id)}
                      className="absolute top-2 right-2 text-red-400 hover:text-red-300 bg-black/50 hover:bg-black/70"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold mb-1 line-clamp-2 text-white">{video.title}</h3>
                    <p className="text-sm text-gray-400 mb-2">{video.creator}</p>
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                      <span className="flex items-center space-x-1">
                        <Eye className="w-3 h-3" />
                        <span>{video.views.toLocaleString()} views</span>
                      </span>
                      <span>{video.likedAt}</span>
                    </div>
                    <Badge variant="outline" className="border-green-500 text-green-400 text-xs">
                      {video.category}
                    </Badge>
                  </CardContent>
                </Card>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  )
}
