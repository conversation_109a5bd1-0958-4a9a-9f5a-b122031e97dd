"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Shield, Fingerprint, Smartphone, Key, AlertTriangle, CheckCircle, Eye, EyeOff } from "lucide-react"

interface SecurityEvent {
  id: string
  type: "login" | "payment" | "profile_change" | "suspicious"
  description: string
  timestamp: Date
  location: string
  device: string
  status: "success" | "failed" | "blocked"
}

export default function EnhancedSecurity() {
  const [biometricEnabled, setBiometricEnabled] = useState(false)
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const [showRecoveryCodes, setShowRecoveryCodes] = useState(false)
  const [securityScore, setSecurityScore] = useState(75)

  const [securityEvents] = useState<SecurityEvent[]>([
    {
      id: "1",
      type: "login",
      description: "Successful login",
      timestamp: new Date(Date.now() - 3600000),
      location: "Johannesburg, GP",
      device: "iPhone 14",
      status: "success",
    },
    {
      id: "2",
      type: "payment",
      description: "Tip sent to BraaiMaster_GP",
      timestamp: new Date(Date.now() - 7200000),
      location: "Johannesburg, GP",
      device: "iPhone 14",
      status: "success",
    },
    {
      id: "3",
      type: "suspicious",
      description: "Login attempt from unusual location",
      timestamp: new Date(Date.now() - 86400000),
      location: "Lagos, Nigeria",
      device: "Unknown Device",
      status: "blocked",
    },
  ])

  const recoveryCodes = [
    "AWEH-2024-SAFE-1234",
    "LEKR-5678-SECU-9012",
    "MZAN-3456-PROT-7890",
    "BOET-1357-SAFE-2468",
    "SHAR-9753-SECU-1357",
  ]

  useEffect(() => {
    // Calculate security score based on enabled features
    let score = 40 // Base score
    if (biometricEnabled) score += 25
    if (twoFactorEnabled) score += 25
    if (true) score += 10 // Strong password (assumed)
    setSecurityScore(score)
  }, [biometricEnabled, twoFactorEnabled])

  const enableBiometric = async () => {
    try {
      // Simulate biometric setup
      if ("credentials" in navigator) {
        setBiometricEnabled(true)
        alert("Biometric authentication enabled successfully!")
      } else {
        alert("Biometric authentication not supported on this device")
      }
    } catch (error) {
      alert("Failed to enable biometric authentication")
    }
  }

  const getEventIcon = (type: string, status: string) => {
    if (status === "blocked") return <AlertTriangle className="w-4 h-4 text-red-400" />
    if (status === "failed") return <AlertTriangle className="w-4 h-4 text-yellow-400" />
    return <CheckCircle className="w-4 h-4 text-green-400" />
  }

  const getSecurityColor = () => {
    if (securityScore >= 90) return "text-green-400"
    if (securityScore >= 70) return "text-yellow-400"
    return "text-red-400"
  }

  return (
    <Card className="bg-gray-900 border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-white">
          <Shield className="w-5 h-5" />
          <span>Enhanced Security</span>
          <Badge className={`${getSecurityColor().replace("text-", "bg-").replace("400", "600")}`}>
            {securityScore}% Secure
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="authentication" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800">
            <TabsTrigger value="authentication">Auth</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
            <TabsTrigger value="privacy">Privacy</TabsTrigger>
            <TabsTrigger value="recovery">Recovery</TabsTrigger>
          </TabsList>

          <TabsContent value="authentication" className="space-y-4">
            {/* Security Score */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-white">Security Score</h4>
                <span className={`text-2xl font-bold ${getSecurityColor()}`}>{securityScore}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all ${
                    securityScore >= 90 ? "bg-green-500" : securityScore >= 70 ? "bg-yellow-500" : "bg-red-500"
                  }`}
                  style={{ width: `${securityScore}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-400 mt-2">
                {securityScore >= 90
                  ? "Excellent security! Your account is well protected."
                  : securityScore >= 70
                    ? "Good security. Consider enabling more features."
                    : "Improve your security by enabling additional features."}
              </p>
            </div>

            {/* Biometric Authentication */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Fingerprint className="w-5 h-5 text-blue-400" />
                  <div>
                    <h4 className="font-medium text-white">Biometric Login</h4>
                    <p className="text-sm text-gray-400">Use fingerprint or face recognition</p>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant={biometricEnabled ? "default" : "outline"}
                  onClick={enableBiometric}
                  disabled={biometricEnabled}
                >
                  {biometricEnabled ? "Enabled" : "Enable"}
                </Button>
              </div>
              {biometricEnabled && (
                <div className="p-2 bg-green-900/20 border border-green-700 rounded text-sm text-green-400">
                  ✓ Biometric authentication is active
                </div>
              )}
            </div>

            {/* Two-Factor Authentication */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Smartphone className="w-5 h-5 text-green-400" />
                  <div>
                    <h4 className="font-medium text-white">Two-Factor Authentication</h4>
                    <p className="text-sm text-gray-400">SMS verification for extra security</p>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant={twoFactorEnabled ? "default" : "outline"}
                  onClick={() => setTwoFactorEnabled(!twoFactorEnabled)}
                >
                  {twoFactorEnabled ? "Enabled" : "Enable"}
                </Button>
              </div>
              {twoFactorEnabled && (
                <div className="space-y-2">
                  <div className="p-2 bg-green-900/20 border border-green-700 rounded text-sm text-green-400">
                    ✓ SMS verification enabled for +27 *** *** 1234
                  </div>
                  <Button size="sm" variant="outline" className="w-full bg-transparent">
                    Change Phone Number
                  </Button>
                </div>
              )}
            </div>

            {/* Password Security */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Key className="w-5 h-5 text-purple-400" />
                  <div>
                    <h4 className="font-medium text-white">Password Security</h4>
                    <p className="text-sm text-gray-400">Last changed 3 months ago</p>
                  </div>
                </div>
                <Button size="sm" variant="outline">
                  Change Password
                </Button>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-gray-300">Strong password strength</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-gray-300">No known data breaches</span>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            {/* Recent Security Events */}
            <div className="space-y-3">
              <h4 className="font-medium text-white">Recent Security Activity</h4>
              {securityEvents.map((event) => (
                <div key={event.id} className="p-3 bg-gray-800 rounded-lg">
                  <div className="flex items-start space-x-3">
                    {getEventIcon(event.type, event.status)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-white">{event.description}</span>
                        <Badge
                          variant={
                            event.status === "success"
                              ? "default"
                              : event.status === "blocked"
                                ? "destructive"
                                : "secondary"
                          }
                        >
                          {event.status}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-400">
                        <p>
                          {event.location} • {event.device}
                        </p>
                        <p>{event.timestamp.toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Security Alerts */}
            <div className="p-4 bg-orange-900/20 border border-orange-700 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="w-5 h-5 text-orange-400" />
                <h4 className="font-medium text-orange-400">Security Alert</h4>
              </div>
              <p className="text-sm text-gray-300 mb-3">
                We blocked a suspicious login attempt from Lagos, Nigeria. If this wasn't you, your account is safe.
              </p>
              <Button size="sm" className="bg-orange-600 hover:bg-orange-700">
                Review Security
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="privacy" className="space-y-4">
            {/* Privacy Settings */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Privacy Controls</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Profile Visibility</span>
                    <p className="text-sm text-gray-400">Who can see your profile</p>
                  </div>
                  <Button size="sm" variant="outline">
                    Public
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Location Sharing</span>
                    <p className="text-sm text-gray-400">Share location with streams</p>
                  </div>
                  <Button size="sm" variant="outline">
                    Friends Only
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Activity Status</span>
                    <p className="text-sm text-gray-400">Show when you're online</p>
                  </div>
                  <Button size="sm" variant="default">
                    On
                  </Button>
                </div>
              </div>
            </div>

            {/* Data Management */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Data Management</h4>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start bg-transparent">
                  Download My Data
                </Button>
                <Button variant="outline" className="w-full justify-start bg-transparent">
                  Delete Account
                </Button>
                <Button variant="outline" className="w-full justify-start bg-transparent">
                  Privacy Policy
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="recovery" className="space-y-4">
            {/* Recovery Codes */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-white">Recovery Codes</h4>
                <Button size="sm" variant="outline" onClick={() => setShowRecoveryCodes(!showRecoveryCodes)}>
                  {showRecoveryCodes ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
              <p className="text-sm text-gray-400 mb-3">
                Save these codes in a safe place. You can use them to recover your account if you lose access.
              </p>
              {showRecoveryCodes ? (
                <div className="space-y-2">
                  {recoveryCodes.map((code, index) => (
                    <div key={index} className="p-2 bg-gray-700 rounded font-mono text-sm text-green-400">
                      {code}
                    </div>
                  ))}
                  <Button size="sm" className="w-full bg-green-600 hover:bg-green-700">
                    Download Codes
                  </Button>
                </div>
              ) : (
                <div className="p-4 bg-gray-700 rounded text-center">
                  <p className="text-gray-400">Click the eye icon to reveal recovery codes</p>
                </div>
              )}
            </div>

            {/* Account Recovery */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Account Recovery Options</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Recovery Email</span>
                    <p className="text-sm text-gray-400"><EMAIL></p>
                  </div>
                  <Button size="sm" variant="outline">
                    Verify
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white">Recovery Phone</span>
                    <p className="text-sm text-gray-400">+27 *** *** 1234</p>
                  </div>
                  <Button size="sm" variant="default">
                    Verified
                  </Button>
                </div>
              </div>
            </div>

            {/* Emergency Contact */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Emergency Contact</h4>
              <p className="text-sm text-gray-400 mb-3">Add a trusted contact who can help you recover your account</p>
              <div className="space-y-2">
                <Input placeholder="Emergency contact email" className="bg-gray-700 border-gray-600 text-white" />
                <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700">
                  Add Emergency Contact
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
