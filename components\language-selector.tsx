"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Globe, Volume2 } from "lucide-react"

const languages = [
  { code: "en", name: "English", flag: "🇬🇧", native: "English" },
  { code: "af", name: "Afrikaans", flag: "🇿🇦", native: "Afrikaans" },
  { code: "zu", name: "<PERSON>ulu", flag: "🇿🇦", native: "isiZulu" },
  { code: "xh", name: "Xhosa", flag: "🇿🇦", native: "isiXhosa" },
  { code: "st", name: "Sotho", flag: "🇿🇦", native: "Sesotho" },
  { code: "tn", name: "Tswana", flag: "🇿🇦", native: "Setswana" },
  { code: "ve", name: "<PERSON><PERSON><PERSON>", flag: "🇿🇦", native: "Tshivenḓa" },
]

const translations = {
  en: {
    welcome: "Welcome to Aweh.tv",
    subtitle: "The home of South African live streaming",
    tagline: "Say it. See it. Stream it.",
    browse: "Browse",
    goLive: "Go Live",
    chat: "Chat",
    likes: "Likes",
    profile: "Profile",
  },
  af: {
    welcome: "Welkom by Aweh.tv",
    subtitle: "Die tuiste van Suid-Afrikaanse lewendige stroming",
    tagline: "Sê dit. Sien dit. Stroom dit.",
    browse: "Blaai",
    goLive: "Gaan Lewendig",
    chat: "Klets",
    likes: "Likes",
    profile: "Profiel",
  },
  zu: {
    welcome: "Siyakwamukela ku-Aweh.tv",
    subtitle: "Ikhaya lokusakaza ngqo eNingizimu Afrika",
    tagline: "Kusho. Kubone. Kusakaze.",
    browse: "Bhrawuza",
    goLive: "Yiya Bukhoma",
    chat: "Xoxa",
    likes: "Izinto ozithandayo",
    profile: "Iphrofayela",
  },
  xh: {
    welcome: "Wamkelekile ku-Aweh.tv",
    subtitle: "Ikhaya lokuphila ngqo eMzantsi Afrika",
    tagline: "Yitsho. Yibone. Yisasaze.",
    browse: "Khangela",
    goLive: "Yiya Buphilweni",
    chat: "Thetha",
    likes: "Izinto ozithandayo",
    profile: "Iprofayile",
  },
}

export default function LanguageSelector() {
  const [selectedLanguage, setSelectedLanguage] = useState("en")
  const [autoTranslate, setAutoTranslate] = useState(false)

  const currentTranslations = translations[selectedLanguage as keyof typeof translations] || translations.en

  return (
    <Card className="bg-gray-900 border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-white">
          <Globe className="w-5 h-5" />
          <span>Language / Taal / Ulimi</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Language Selection */}
        <div>
          <label className="text-sm font-medium text-gray-300 mb-2 block">Select Language</label>
          <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
            <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border-gray-600">
              {languages.map((lang) => (
                <SelectItem key={lang.code} value={lang.code} className="text-white">
                  <div className="flex items-center space-x-2">
                    <span>{lang.flag}</span>
                    <span>{lang.native}</span>
                    <span className="text-gray-400">({lang.name})</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Preview */}
        <div className="p-3 bg-gray-800 rounded-lg">
          <h4 className="font-bold text-green-400 mb-1">{currentTranslations.welcome}</h4>
          <p className="text-sm text-gray-300 mb-1">{currentTranslations.subtitle}</p>
          <p className="text-xs text-gray-400">{currentTranslations.tagline}</p>
        </div>

        {/* Auto-translate Chat */}
        <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
          <div className="flex items-center space-x-2">
            <Volume2 className="w-4 h-4 text-blue-400" />
            <div>
              <div className="text-sm font-medium text-white">Auto-translate Chat</div>
              <div className="text-xs text-gray-400">Translate messages to your language</div>
            </div>
          </div>
          <Button
            size="sm"
            variant={autoTranslate ? "default" : "outline"}
            onClick={() => setAutoTranslate(!autoTranslate)}
          >
            {autoTranslate ? "On" : "Off"}
          </Button>
        </div>

        {/* Popular SA Languages */}
        <div>
          <h5 className="text-sm font-medium text-gray-300 mb-2">Quick Select</h5>
          <div className="flex flex-wrap gap-2">
            {languages.slice(0, 4).map((lang) => (
              <Badge
                key={lang.code}
                variant={selectedLanguage === lang.code ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setSelectedLanguage(lang.code)}
              >
                {lang.flag} {lang.native}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
