"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Video, VideoOff, Mic, MicOff, Settings, Users, Eye, Share, Camera, Monitor, Smartphone } from "lucide-react"
import StreamChat from "@/components/stream-chat"

const categories = ["Gaming", "Music", "Cooking", "Travel", "Sports", "Education", "Comedy", "Art", "Just Chatting"]

export default function StreamPage() {
  const [isLive, setIsLive] = useState(false)
  const [isVideoOn, setIsVideoOn] = useState(true)
  const [isAudioOn, setIsAudioOn] = useState(true)
  const [streamTitle, setStreamTitle] = useState("")
  const [streamDescription, setStreamDescription] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("")
  const [viewerCount, setViewerCount] = useState(0)
  const [streamDuration, setStreamDuration] = useState(0)
  const [streamSource, setStreamSource] = useState("camera") // camera, screen, mobile
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    // Simulate viewer count changes and stream duration
    if (isLive) {
      const viewerInterval = setInterval(() => {
        setViewerCount((prev) => Math.max(0, prev + Math.floor(Math.random() * 10) - 4))
      }, 3000)

      const durationInterval = setInterval(() => {
        setStreamDuration((prev) => prev + 1)
      }, 1000)

      return () => {
        clearInterval(viewerInterval)
        clearInterval(durationInterval)
      }
    }
  }, [isLive])

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const startStream = async () => {
    try {
      let stream: MediaStream

      if (streamSource === "screen") {
        stream = await navigator.mediaDevices.getDisplayMedia({
          video: true,
          audio: true,
        })
      } else {
        stream = await navigator.mediaDevices.getUserMedia({
          video: streamSource === "camera",
          audio: true,
        })
      }

      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
      setIsLive(true)
      setViewerCount(Math.floor(Math.random() * 20) + 5)
      setStreamDuration(0)
    } catch (error) {
      console.error("Error accessing media devices:", error)
      alert("Could not access camera/microphone. Please check permissions.")
    }
  }

  const stopStream = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      stream.getTracks().forEach((track) => track.stop())
    }
    setIsLive(false)
    setViewerCount(0)
    setStreamDuration(0)
  }

  const toggleVideo = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      const videoTrack = stream.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled
        setIsVideoOn(videoTrack.enabled)
      }
    }
  }

  const toggleAudio = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream
      const audioTrack = stream.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled
        setIsAudioOn(audioTrack.enabled)
      }
    }
  }

  const shareStream = () => {
    if (navigator.share && isLive) {
      navigator.share({
        title: streamTitle || "Live Stream on Aweh.tv",
        text: "Check out this lekker stream!",
        url: window.location.href,
      })
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert("Stream link copied to clipboard!")
    }
  }

  return (
    <div className="min-h-screen bg-black text-white pb-20">
      {/* Mobile Header */}
      <div className="md:hidden sticky top-0 z-50 bg-black/90 backdrop-blur-sm border-b border-gray-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold">Go Live</h1>
            {isLive && (
              <div className="flex items-center space-x-2">
                <Badge className="bg-red-500">LIVE</Badge>
                <div className="text-sm text-gray-300">{formatDuration(streamDuration)}</div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <div className="grid lg:grid-cols-3 gap-6">
          {/* Main Stream Area */}
          <div className="lg:col-span-2 space-y-6">
            {/* Stream Setup - Only show when not live */}
            {!isLive && (
              <Card className="bg-gray-900 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white">Stream Setup</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="title" className="text-white">
                      Stream Title *
                    </Label>
                    <Input
                      id="title"
                      value={streamTitle}
                      onChange={(e) => setStreamTitle(e.target.value)}
                      placeholder="What's your stream about?"
                      className="bg-gray-800 border-gray-600 text-white"
                    />
                  </div>

                  <div>
                    <Label htmlFor="category" className="text-white">
                      Category *
                    </Label>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-600">
                        {categories.map((category) => (
                          <SelectItem key={category} value={category} className="text-white">
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="description" className="text-white">
                      Description
                    </Label>
                    <Textarea
                      id="description"
                      value={streamDescription}
                      onChange={(e) => setStreamDescription(e.target.value)}
                      placeholder="Tell viewers what to expect..."
                      rows={3}
                      className="bg-gray-800 border-gray-600 text-white"
                    />
                  </div>

                  <div>
                    <Label className="text-white">Stream Source</Label>
                    <div className="grid grid-cols-3 gap-2 mt-2">
                      <Button
                        variant={streamSource === "camera" ? "default" : "outline"}
                        onClick={() => setStreamSource("camera")}
                        className="flex flex-col items-center p-4 h-auto"
                      >
                        <Camera className="w-6 h-6 mb-2" />
                        <span className="text-xs">Camera</span>
                      </Button>
                      <Button
                        variant={streamSource === "screen" ? "default" : "outline"}
                        onClick={() => setStreamSource("screen")}
                        className="flex flex-col items-center p-4 h-auto"
                      >
                        <Monitor className="w-6 h-6 mb-2" />
                        <span className="text-xs">Screen</span>
                      </Button>
                      <Button
                        variant={streamSource === "mobile" ? "default" : "outline"}
                        onClick={() => setStreamSource("mobile")}
                        className="flex flex-col items-center p-4 h-auto"
                      >
                        <Smartphone className="w-6 h-6 mb-2" />
                        <span className="text-xs">Mobile</span>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Video Preview/Stream */}
            <Card className="bg-gray-900 border-gray-700">
              <CardHeader className="flex flex-row items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CardTitle className="text-white">{isLive ? streamTitle || "Live Stream" : "Preview"}</CardTitle>
                  {isLive && <Badge className="bg-red-500">LIVE</Badge>}
                </div>
                {isLive && (
                  <div className="flex items-center space-x-4 text-sm text-gray-300">
                    <div className="flex items-center space-x-1">
                      <Eye className="w-4 h-4" />
                      <span>{viewerCount} viewers</span>
                    </div>
                    <div>{formatDuration(streamDuration)}</div>
                  </div>
                )}
              </CardHeader>
              <CardContent>
                <div className="relative bg-black rounded-lg overflow-hidden aspect-video">
                  <video ref={videoRef} autoPlay muted className="w-full h-full object-cover" playsInline />
                  {!isVideoOn && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
                      <VideoOff className="w-16 h-16 text-gray-400" />
                    </div>
                  )}
                  {!isLive && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-800/50">
                      <div className="text-center">
                        <Camera className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-400">Stream preview will appear here</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Stream Controls */}
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={isVideoOn ? "default" : "destructive"}
                      size="sm"
                      onClick={toggleVideo}
                      disabled={!isLive}
                    >
                      {isVideoOn ? <Video className="w-4 h-4" /> : <VideoOff className="w-4 h-4" />}
                    </Button>
                    <Button
                      variant={isAudioOn ? "default" : "destructive"}
                      size="sm"
                      onClick={toggleAudio}
                      disabled={!isLive}
                    >
                      {isAudioOn ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="w-4 h-4" />
                    </Button>
                    {isLive && (
                      <Button variant="outline" size="sm" onClick={shareStream}>
                        <Share className="w-4 h-4" />
                      </Button>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    {!isLive ? (
                      <Button
                        onClick={startStream}
                        className="bg-red-600 hover:bg-red-700"
                        disabled={!streamTitle.trim() || !selectedCategory}
                      >
                        Go Live
                      </Button>
                    ) : (
                      <Button onClick={stopStream} variant="destructive">
                        End Stream
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Chat Sidebar */}
          <div className="space-y-6">
            <StreamChat isLive={isLive} />

            {/* Stream Info */}
            {isLive && (
              <Card className="bg-gray-900 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-white">
                    <Users className="w-5 h-5" />
                    <span>Stream Info</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-white">{streamTitle}</h4>
                    <p className="text-sm text-gray-300 mt-1">{streamDescription}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Viewers</span>
                      <span className="font-semibold text-white">{viewerCount}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Duration</span>
                      <span className="font-semibold text-white">{formatDuration(streamDuration)}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-400">Category</span>
                      <Badge variant="outline" className="border-green-500 text-green-400">
                        {selectedCategory}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
