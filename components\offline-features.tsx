"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Download, Wifi, WifiOff, Play, Trash2, Clock, HardDrive } from "lucide-react"

interface DownloadedVideo {
  id: string
  title: string
  creator: string
  duration: string
  size: number
  downloadedAt: Date
  quality: string
  thumbnail: string
}

interface OfflineMessage {
  id: string
  content: string
  timestamp: Date
  recipient: string
}

export default function OfflineFeatures() {
  const [isOnline, setIsOnline] = useState(true)
  const [downloadedVideos, setDownloadedVideos] = useState<DownloadedVideo[]>([
    {
      id: "1",
      title: "Best Braai Tips for Summer",
      creator: "BraaiMaster",
      duration: "12:34",
      size: 145.2,
      downloadedAt: new Date(Date.now() - 86400000),
      quality: "720p",
      thumbnail: "/south-african-braai-tips.png",
    },
    {
      id: "2",
      title: "Cape Town Sunset Vibes",
      creator: "CapeTownVibes",
      duration: "18:45",
      size: 234.8,
      downloadedAt: new Date(Date.now() - 172800000),
      quality: "480p",
      thumbnail: "/cape-town-sunset.png",
    },
  ])
  const [offlineMessages, setOfflineMessages] = useState<OfflineMessage[]>([])
  const [storageUsed, setStorageUsed] = useState(380)
  const [storageLimit] = useState(2000)

  useEffect(() => {
    // Monitor online/offline status
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener("online", handleOnline)
    window.addEventListener("offline", handleOffline)

    setIsOnline(navigator.onLine)

    return () => {
      window.removeEventListener("online", handleOnline)
      window.removeEventListener("offline", handleOffline)
    }
  }, [])

  const deleteDownload = (id: string) => {
    const video = downloadedVideos.find((v) => v.id === id)
    if (video) {
      setDownloadedVideos((prev) => prev.filter((v) => v.id !== id))
      setStorageUsed((prev) => prev - video.size)
    }
  }

  const formatFileSize = (mb: number) => {
    if (mb > 1000) {
      return `${(mb / 1000).toFixed(1)} GB`
    }
    return `${mb.toFixed(1)} MB`
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return "Yesterday"
    if (diffDays < 7) return `${diffDays} days ago`
    return date.toLocaleDateString()
  }

  const syncOfflineData = () => {
    // Simulate syncing offline messages and data
    alert("Syncing offline data... This would send queued messages and update content.")
  }

  return (
    <Card className="bg-gray-900 border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-white">
          {isOnline ? <Wifi className="w-5 h-5 text-green-400" /> : <WifiOff className="w-5 h-5 text-red-400" />}
          <span>Offline Features</span>
          <Badge variant={isOnline ? "default" : "destructive"}>{isOnline ? "Online" : "Offline"}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="downloads" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-gray-800">
            <TabsTrigger value="downloads">Downloads</TabsTrigger>
            <TabsTrigger value="storage">Storage</TabsTrigger>
            <TabsTrigger value="sync">Sync</TabsTrigger>
          </TabsList>

          <TabsContent value="downloads" className="space-y-4">
            {/* Download Queue Status */}
            {!isOnline && (
              <div className="p-3 bg-orange-900/20 border border-orange-700 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <WifiOff className="w-4 h-4 text-orange-400" />
                  <span className="font-medium text-orange-400">Offline Mode</span>
                </div>
                <p className="text-sm text-gray-300">
                  You can still watch downloaded videos. New downloads will start when you're back online.
                </p>
              </div>
            )}

            {/* Downloaded Videos */}
            <div className="space-y-3">
              {downloadedVideos.length === 0 ? (
                <div className="text-center py-8">
                  <Download className="w-12 h-12 text-gray-600 mx-auto mb-3" />
                  <h3 className="font-medium text-gray-400 mb-2">No Downloaded Videos</h3>
                  <p className="text-sm text-gray-500 mb-4">Download videos to watch offline</p>
                  <Button className="bg-green-600 hover:bg-green-700" disabled={!isOnline}>
                    <Download className="w-4 h-4 mr-2" />
                    Browse Videos
                  </Button>
                </div>
              ) : (
                downloadedVideos.map((video) => (
                  <div key={video.id} className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
                    <div className="relative w-16 h-12 flex-shrink-0">
                      <img
                        src={video.thumbnail || "/placeholder.svg"}
                        alt={video.title}
                        className="w-full h-full object-cover rounded"
                      />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Play className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-white text-sm line-clamp-1">{video.title}</h4>
                      <p className="text-xs text-gray-400">{video.creator}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {video.quality}
                        </Badge>
                        <span className="text-xs text-gray-500">{video.duration}</span>
                        <span className="text-xs text-gray-500">{formatFileSize(video.size)}</span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Downloaded {formatDate(video.downloadedAt)}</p>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => deleteDownload(video.id)}
                      className="text-red-400 hover:text-red-300"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="storage" className="space-y-4">
            {/* Storage Overview */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <HardDrive className="w-5 h-5 text-blue-400" />
                  <span className="font-medium text-white">Storage Usage</span>
                </div>
                <span className="text-sm text-gray-400">
                  {formatFileSize(storageUsed)} / {formatFileSize(storageLimit)}
                </span>
              </div>
              <Progress value={(storageUsed / storageLimit) * 100} className="w-full mb-2" />
              <p className="text-xs text-gray-500">{formatFileSize(storageLimit - storageUsed)} available</p>
            </div>

            {/* Storage Breakdown */}
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-white">Downloaded Videos</span>
                </div>
                <span className="text-sm text-gray-400">{formatFileSize(storageUsed)}</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-white">Cached Data</span>
                </div>
                <span className="text-sm text-gray-400">45.2 MB</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm text-white">App Data</span>
                </div>
                <span className="text-sm text-gray-400">12.8 MB</span>
              </div>
            </div>

            {/* Storage Actions */}
            <div className="space-y-2">
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <Trash2 className="w-4 h-4 mr-2" />
                Clear Cache (45.2 MB)
              </Button>
              <Button variant="outline" className="w-full justify-start bg-transparent">
                <Download className="w-4 h-4 mr-2" />
                Manage Downloads
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="sync" className="space-y-4">
            {/* Sync Status */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Clock className="w-5 h-5 text-yellow-400" />
                  <span className="font-medium text-white">Sync Status</span>
                </div>
                <Badge variant={isOnline ? "default" : "secondary"}>{isOnline ? "Up to date" : "Pending sync"}</Badge>
              </div>
              <p className="text-sm text-gray-400">
                {isOnline ? "All data is synchronized with the server" : "Some data will sync when you're back online"}
              </p>
            </div>

            {/* Offline Actions Queue */}
            <div className="p-4 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Queued Actions</h4>
              {offlineMessages.length === 0 ? (
                <p className="text-sm text-gray-400">No pending actions</p>
              ) : (
                <div className="space-y-2">
                  {offlineMessages.map((message) => (
                    <div key={message.id} className="p-2 bg-gray-700 rounded text-sm">
                      <p className="text-white">{message.content}</p>
                      <p className="text-xs text-gray-400 mt-1">To: {message.recipient}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Sync Actions */}
            <div className="space-y-2">
              <Button onClick={syncOfflineData} disabled={!isOnline} className="w-full bg-green-600 hover:bg-green-700">
                <Download className="w-4 h-4 mr-2" />
                {isOnline ? "Sync Now" : "Waiting for connection..."}
              </Button>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm">
                  Auto-sync: On
                </Button>
                <Button variant="outline" size="sm">
                  WiFi only: On
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
