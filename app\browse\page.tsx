"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, Play, Eye, Menu, Heart, Share, MessageCircle } from "lucide-react"
import Link from "next/link"

const featuredStreams = [
  {
    id: 1,
    title: "Gaming with the Boyz - Fortnite Tournament",
    streamer: "GamerSA_Pro",
    viewers: 1234,
    category: "Gaming",
    thumbnail: "/gaming-stream-thumbnail.png",
    isLive: true,
    likes: 234,
    comments: 45,
  },
  {
    id: 2,
    title: "Cooking Traditional Boerewors",
    streamer: "ChefMandla",
    viewers: 567,
    category: "Cooking",
    thumbnail: "/cooking-boerewors-stream.png",
    isLive: true,
    likes: 189,
    comments: 32,
  },
  {
    id: 3,
    title: "Cape Town Sunset Vibes",
    streamer: "CapeTownVibes",
    viewers: 890,
    category: "Travel",
    thumbnail: "/cape-town-sunset.png",
    isLive: true,
    likes: 456,
    comments: 78,
  },
  {
    id: 4,
    title: "Braai Master Class - Lekker Tips",
    streamer: "BraaiKing",
    viewers: 445,
    category: "Cooking",
    thumbnail: "/south-african-braai-tips.png",
    isLive: true,
    likes: 123,
    comments: 29,
  },
]

const categories = ["All", "Gaming", "Music", "Cooking", "Travel", "Sports", "Comedy", "Art"]

export default function BrowsePage() {
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [isMobileView, setIsMobileView] = useState(false)

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Mobile Header */}
      <div className="md:hidden sticky top-0 z-50 bg-black/90 backdrop-blur-sm border-b border-gray-800">
        <div className="flex items-center justify-between p-4">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-red-500 rounded-full flex items-center justify-center">
              <Play className="w-3 h-3 text-white" />
            </div>
            <span className="font-bold text-lg">Aweh.tv</span>
          </Link>
          <div className="flex items-center space-x-4">
            <Search className="w-6 h-6" />
            <Menu className="w-6 h-6" />
          </div>
        </div>

        {/* Mobile Categories */}
        <div className="flex overflow-x-auto px-4 pb-3 space-x-2 scrollbar-hide">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              className={`whitespace-nowrap ${
                selectedCategory === category
                  ? "bg-green-600 text-white"
                  : "bg-transparent border-gray-600 text-gray-300"
              }`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Desktop Header */}
      <div className="hidden md:block container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search streams, videos, or creators..."
              className="pl-10 bg-gray-900 border-gray-700 text-white"
            />
          </div>
          <Button className="bg-green-600 hover:bg-green-700">Search</Button>
        </div>

        <div className="flex flex-wrap gap-2 mb-8">
          {categories.map((category) => (
            <Badge
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              className={`cursor-pointer ${
                selectedCategory === category
                  ? "bg-green-600 text-white"
                  : "border-gray-600 text-gray-300 hover:bg-gray-800"
              }`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Badge>
          ))}
        </div>
      </div>

      {/* Mobile TikTok-style Feed */}
      <div className="md:hidden">
        <div className="space-y-1">
          {featuredStreams.map((stream, index) => (
            <div key={stream.id} className="relative h-screen w-full">
              <img
                src={stream.thumbnail || "/placeholder.svg"}
                alt={stream.title}
                className="w-full h-full object-cover"
              />

              {/* Live Badge */}
              <div className="absolute top-4 left-4">
                <Badge className="bg-red-500 text-white font-bold">LIVE</Badge>
              </div>

              {/* Viewer Count */}
              <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full flex items-center space-x-1">
                <Eye className="w-4 h-4" />
                <span className="text-sm font-medium">{stream.viewers.toLocaleString()}</span>
              </div>

              {/* Bottom Info */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                <div className="flex items-end justify-between">
                  <div className="flex-1 pr-4">
                    <h3 className="text-white font-bold text-lg mb-1 line-clamp-2">{stream.title}</h3>
                    <p className="text-gray-300 text-sm mb-2">@{stream.streamer}</p>
                    <Badge variant="outline" className="border-green-500 text-green-400 text-xs">
                      {stream.category}
                    </Badge>
                  </div>

                  {/* Mobile Actions */}
                  <div className="flex flex-col items-center space-y-4">
                    <button className="flex flex-col items-center space-y-1">
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                        <Heart className="w-6 h-6 text-white" />
                      </div>
                      <span className="text-white text-xs">{stream.likes}</span>
                    </button>

                    <button className="flex flex-col items-center space-y-1">
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                        <MessageCircle className="w-6 h-6 text-white" />
                      </div>
                      <span className="text-white text-xs">{stream.comments}</span>
                    </button>

                    <button className="flex flex-col items-center space-y-1">
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                        <Share className="w-6 h-6 text-white" />
                      </div>
                      <span className="text-white text-xs">Share</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Play Button Overlay */}
              <Link href={`/watch/${stream.id}`} className="absolute inset-0 flex items-center justify-center">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                  <Play className="w-8 h-8 text-white ml-1" />
                </div>
              </Link>
            </div>
          ))}
        </div>
      </div>

      {/* Desktop Grid View */}
      <div className="hidden md:block container mx-auto px-4">
        <section className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Live Now</h2>
            <Button variant="outline" className="border-gray-600 text-gray-300 bg-transparent">
              View All
            </Button>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {featuredStreams.map((stream) => (
              <Link key={stream.id} href={`/watch/${stream.id}`}>
                <Card className="bg-gray-900 border-gray-700 overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="relative">
                    <img
                      src={stream.thumbnail || "/placeholder.svg"}
                      alt={stream.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-2 left-2">
                      <Badge className="bg-red-500">LIVE</Badge>
                    </div>
                    <div className="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-sm flex items-center space-x-1">
                      <Eye className="w-3 h-3" />
                      <span>{stream.viewers.toLocaleString()}</span>
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/50 transition-opacity">
                      <Play className="w-12 h-12 text-white" />
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold mb-1 line-clamp-2 text-white">{stream.title}</h3>
                    <p className="text-sm text-gray-400 mb-2">{stream.streamer}</p>
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="text-xs border-green-500 text-green-400">
                        {stream.category}
                      </Badge>
                      <div className="flex items-center space-x-3 text-xs text-gray-400">
                        <span className="flex items-center space-x-1">
                          <Heart className="w-3 h-3" />
                          <span>{stream.likes}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <MessageCircle className="w-3 h-3" />
                          <span>{stream.comments}</span>
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </section>
      </div>
    </div>
  )
}
