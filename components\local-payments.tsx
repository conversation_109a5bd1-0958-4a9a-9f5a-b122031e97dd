"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Gift } from "lucide-react"

const paymentMethods = [
  { id: "snapscan", name: "SnapScan", icon: "📱", color: "bg-blue-600" },
  { id: "zapper", name: "<PERSON>apper", icon: "⚡", color: "bg-purple-600" },
  { id: "capitec", name: "Capitec Pay", icon: "🏦", color: "bg-red-600" },
  { id: "fnb", name: "FNB Pay", icon: "🏛️", color: "bg-yellow-600" },
  { id: "eft", name: "EFT Transfer", icon: "💳", color: "bg-green-600" },
  { id: "airtime", name: "Airtime", icon: "📞", color: "bg-orange-600" },
]

const tipAmounts = [
  { amount: 10, label: "Coffee", emoji: "☕", description: "Buy me a coffee" },
  { amount: 25, label: "Boerewors", emoji: "🌭", description: "Boerewors roll fund" },
  { amount: 50, label: "Braai", emoji: "🔥", description: "Braai contribution" },
  { amount: 100, label: "Biltong", emoji: "🥩", description: "Biltong money" },
  { amount: 200, label: "Lekker", emoji: "🎉", description: "That was lekker!" },
]

export default function LocalPayments() {
  const [selectedMethod, setSelectedMethod] = useState("snapscan")
  const [tipAmount, setTipAmount] = useState(25)
  const [customAmount, setCustomAmount] = useState("")
  const [message, setMessage] = useState("")

  const handleTip = () => {
    const amount = customAmount ? Number.parseFloat(customAmount) : tipAmount
    alert(`Sending R${amount} tip via ${paymentMethods.find((m) => m.id === selectedMethod)?.name}!`)
  }

  return (
    <Card className="bg-gray-900 border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-white">
          <Gift className="w-5 h-5" />
          <span>Support Creator</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="tip" className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-gray-800">
            <TabsTrigger value="tip" className="data-[state=active]:bg-green-600">
              Send Tip
            </TabsTrigger>
            <TabsTrigger value="methods" className="data-[state=active]:bg-green-600">
              Payment Methods
            </TabsTrigger>
          </TabsList>

          <TabsContent value="tip" className="space-y-4">
            {/* Quick Tip Amounts */}
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-3">Quick Tips</h4>
              <div className="grid grid-cols-2 gap-2">
                {tipAmounts.map((tip) => (
                  <Button
                    key={tip.amount}
                    variant={tipAmount === tip.amount ? "default" : "outline"}
                    size="sm"
                    onClick={() => {
                      setTipAmount(tip.amount)
                      setCustomAmount("")
                    }}
                    className="flex flex-col items-center p-3 h-auto"
                  >
                    <span className="text-lg mb-1">{tip.emoji}</span>
                    <span className="text-xs font-medium">R{tip.amount}</span>
                    <span className="text-xs text-gray-400">{tip.label}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Custom Amount */}
            <div>
              <label className="text-sm font-medium text-gray-300 mb-2 block">Custom Amount (ZAR)</label>
              <Input
                type="number"
                placeholder="Enter amount"
                value={customAmount}
                onChange={(e) => setCustomAmount(e.target.value)}
                className="bg-gray-800 border-gray-600 text-white"
              />
            </div>

            {/* Message */}
            <div>
              <label className="text-sm font-medium text-gray-300 mb-2 block">Message (Optional)</label>
              <Input
                placeholder="Say something lekker..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="bg-gray-800 border-gray-600 text-white"
              />
            </div>

            {/* Send Button */}
            <Button onClick={handleTip} className="w-full bg-green-600 hover:bg-green-700">
              <Gift className="w-4 h-4 mr-2" />
              Send R{customAmount || tipAmount} Tip
            </Button>
          </TabsContent>

          <TabsContent value="methods" className="space-y-4">
            {/* Payment Method Selection */}
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-3">Select Payment Method</h4>
              <div className="space-y-2">
                {paymentMethods.map((method) => (
                  <Button
                    key={method.id}
                    variant={selectedMethod === method.id ? "default" : "outline"}
                    onClick={() => setSelectedMethod(method.id)}
                    className="w-full justify-start"
                  >
                    <span className="text-lg mr-3">{method.icon}</span>
                    <span>{method.name}</span>
                    {method.id === "airtime" && <Badge className="ml-auto bg-orange-600">Popular</Badge>}
                  </Button>
                ))}
              </div>
            </div>

            {/* Payment Info */}
            <div className="p-3 bg-gray-800 rounded-lg">
              <h5 className="font-medium text-white mb-2">Payment Information</h5>
              {selectedMethod === "snapscan" && (
                <p className="text-sm text-gray-300">Scan QR code with SnapScan app to send payment</p>
              )}
              {selectedMethod === "zapper" && (
                <p className="text-sm text-gray-300">Use Zapper app to make instant payment</p>
              )}
              {selectedMethod === "eft" && (
                <p className="text-sm text-gray-300">Bank transfer via EFT - instant notification</p>
              )}
              {selectedMethod === "airtime" && (
                <p className="text-sm text-gray-300">Convert airtime to tip - popular with mobile users</p>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
