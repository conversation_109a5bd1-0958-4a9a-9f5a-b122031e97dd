"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, Users, Flag } from "lucide-react"

const saCategories = [
  {
    id: "braai",
    name: "Braai & Chill",
    icon: "🔥",
    description: "Traditional SA braai culture",
    color: "bg-orange-600",
  },
  {
    id: "mzansi-music",
    name: "<PERSON>zansi Music",
    icon: "🎵",
    description: "Local SA music and artists",
    color: "bg-purple-600",
  },
  { id: "rugby", name: "Rugby", icon: "🏉", description: "Springboks and local rugby", color: "bg-green-600" },
  { id: "cricket", name: "Cricket", icon: "🏏", description: "Proteas and cricket culture", color: "bg-blue-600" },
  { id: "township", name: "Township Tours", icon: "🏘️", description: "Explore SA townships", color: "bg-yellow-600" },
  { id: "heritage", name: "Heritage", icon: "🏛️", description: "SA history and culture", color: "bg-red-600" },
  { id: "wildlife", name: "Wildlife", icon: "🦁", description: "Big 5 and SA nature", color: "bg-green-700" },
  { id: "comedy", name: "SA Comedy", icon: "😂", description: "Local humor and comedy", color: "bg-pink-600" },
]

const saEvents = [
  {
    id: 1,
    name: "Heritage Day Celebration",
    date: "24 September",
    description: "Celebrate SA's diverse cultures",
    participants: 1250,
    trending: true,
  },
  {
    id: 2,
    name: "Springboks vs All Blacks",
    date: "This Saturday",
    description: "Watch party for the big match",
    participants: 3400,
    trending: true,
  },
  {
    id: 3,
    name: "Braai Day Challenge",
    date: "24 September",
    description: "Show off your braai skills",
    participants: 890,
    trending: false,
  },
  {
    id: 4,
    name: "Youth Day Streams",
    date: "16 June",
    description: "Young SA creators showcase",
    participants: 2100,
    trending: false,
  },
]

const saSlang = [
  { word: "Howzit", meaning: "Hello, how are you?", usage: "Greeting" },
  { word: "Lekker", meaning: "Nice, good, cool", usage: "Positive expression" },
  { word: "Sharp", meaning: "Cool, alright, goodbye", usage: "Agreement/farewell" },
  { word: "Eish", meaning: "Oh no, expression of dismay", usage: "Exclamation" },
  { word: "Aweh", meaning: "Hello, what's up", usage: "Casual greeting" },
  { word: "Boet", meaning: "Brother, friend", usage: "Friendly address" },
  { word: "My bru", meaning: "My friend/brother", usage: "Friendly address" },
  { word: "Sho", meaning: "Yes, okay", usage: "Agreement" },
]

const provinces = [
  { code: "WC", name: "Western Cape", capital: "Cape Town", emoji: "🏔️", color: "bg-blue-500" },
  { code: "GP", name: "Gauteng", capital: "Johannesburg", emoji: "🏙️", color: "bg-yellow-500" },
  { code: "KZN", name: "KwaZulu-Natal", capital: "Durban", emoji: "🌊", color: "bg-green-500" },
  { code: "EC", name: "Eastern Cape", capital: "Port Elizabeth", emoji: "🦓", color: "bg-purple-500" },
  { code: "FS", name: "Free State", capital: "Bloemfontein", emoji: "🌻", color: "bg-orange-500" },
  { code: "LP", name: "Limpopo", capital: "Polokwane", emoji: "🌳", color: "bg-green-700" },
  { code: "MP", name: "Mpumalanga", capital: "Nelspruit", emoji: "🦁", color: "bg-red-500" },
  { code: "NC", name: "Northern Cape", capital: "Kimberley", emoji: "💎", color: "bg-gray-500" },
  { code: "NW", name: "North West", capital: "Mahikeng", emoji: "🌾", color: "bg-yellow-700" },
]

export default function SACulturalFeatures() {
  const [selectedCategory, setSelectedCategory] = useState("braai")
  const [joinedEvents, setJoinedEvents] = useState<number[]>([])

  const joinEvent = (eventId: number) => {
    setJoinedEvents((prev) => [...prev, eventId])
  }

  return (
    <div className="space-y-6">
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <Flag className="w-5 h-5" />
            <span>Proudly South African</span>
            <span className="text-2xl">🇿🇦</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="categories" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-gray-800">
              <TabsTrigger value="categories">Categories</TabsTrigger>
              <TabsTrigger value="events">Events</TabsTrigger>
              <TabsTrigger value="provinces">Provinces</TabsTrigger>
              <TabsTrigger value="slang">Slang</TabsTrigger>
            </TabsList>

            <TabsContent value="categories" className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                {saCategories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    onClick={() => setSelectedCategory(category.id)}
                    className="flex flex-col items-center p-4 h-auto space-y-2"
                  >
                    <span className="text-2xl">{category.icon}</span>
                    <span className="text-sm font-medium">{category.name}</span>
                    <span className="text-xs text-gray-400 text-center">{category.description}</span>
                  </Button>
                ))}
              </div>

              {/* Featured Category Content */}
              <div className="p-4 bg-gray-800 rounded-lg">
                <h4 className="font-bold text-white mb-2">
                  {saCategories.find((c) => c.id === selectedCategory)?.name} Streams
                </h4>
                <p className="text-sm text-gray-400 mb-3">
                  {saCategories.find((c) => c.id === selectedCategory)?.description}
                </p>
                <div className="flex space-x-2">
                  <Badge className="bg-green-600">234 Live</Badge>
                  <Badge variant="outline">1.2K Videos</Badge>
                  <Badge variant="outline">45K Followers</Badge>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="events" className="space-y-4">
              {saEvents.map((event) => (
                <div key={event.id} className="p-4 bg-gray-800 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-bold text-white">{event.name}</h4>
                        {event.trending && <Badge className="bg-red-500">Trending</Badge>}
                      </div>
                      <p className="text-sm text-gray-400 mb-2">{event.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>{event.date}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Users className="w-3 h-3" />
                          <span>{event.participants.toLocaleString()} joined</span>
                        </span>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => joinEvent(event.id)}
                      disabled={joinedEvents.includes(event.id)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      {joinedEvents.includes(event.id) ? "Joined" : "Join"}
                    </Button>
                  </div>
                </div>
              ))}
            </TabsContent>

            <TabsContent value="provinces" className="space-y-4">
              <div className="grid grid-cols-3 gap-3">
                {provinces.map((province) => (
                  <div key={province.code} className="p-3 bg-gray-800 rounded-lg text-center">
                    <div className="text-2xl mb-1">{province.emoji}</div>
                    <div className="font-bold text-white text-sm">{province.code}</div>
                    <div className="text-xs text-gray-400">{province.capital}</div>
                    <Badge className={`mt-2 text-xs ${province.color}`}>
                      {Math.floor(Math.random() * 500) + 100} streams
                    </Badge>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="slang" className="space-y-3">
              {saSlang.map((item, index) => (
                <div key={index} className="p-3 bg-gray-800 rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-bold text-green-400">{item.word}</span>
                    <Badge variant="outline" className="text-xs">
                      {item.usage}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-300">{item.meaning}</p>
                </div>
              ))}
              <div className="p-3 bg-green-900/20 border border-green-700 rounded-lg">
                <h4 className="font-bold text-green-400 mb-2">💡 Pro Tip</h4>
                <p className="text-sm text-gray-300">
                  Use SA slang in your streams to connect with local viewers! The AI chat will understand and respond
                  appropriately.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
