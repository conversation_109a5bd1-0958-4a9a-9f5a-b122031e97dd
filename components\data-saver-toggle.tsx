"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Wifi, WifiOff, Smartphone, BarChart3 } from "lucide-react"

interface DataUsage {
  today: number
  thisMonth: number
  savedToday: number
}

export default function DataSaverToggle() {
  const [dataSaverMode, setDataSaverMode] = useState(false)
  const [dataUsage, setDataUsage] = useState<DataUsage>({
    today: 45.2,
    thisMonth: 1250.8,
    savedToday: 12.3,
  })
  const [connectionType, setConnectionType] = useState<"wifi" | "mobile">("mobile")

  useEffect(() => {
    // Check connection type
    if (typeof navigator !== "undefined" && "connection" in navigator) {
      const connection = (navigator as any).connection
      setConnectionType(connection?.type === "wifi" ? "wifi" : "mobile")
    }

    // Load data saver preference
    const savedPreference = localStorage.getItem("dataSaverMode")
    if (savedPreference) {
      setDataSaverMode(JSON.parse(savedPreference))
    }
  }, [])

  const toggleDataSaver = () => {
    const newMode = !dataSaverMode
    setDataSaverMode(newMode)
    localStorage.setItem("dataSaverMode", JSON.stringify(newMode))
  }

  const formatDataUsage = (mb: number) => {
    if (mb > 1000) {
      return `${(mb / 1000).toFixed(1)} GB`
    }
    return `${mb.toFixed(1)} MB`
  }

  return (
    <Card className="bg-gray-900 border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-white">
          <Smartphone className="w-5 h-5" />
          <span>Data Management</span>
          <Badge variant={connectionType === "wifi" ? "default" : "destructive"} className="ml-2">
            {connectionType === "wifi" ? (
              <>
                <Wifi className="w-3 h-3 mr-1" />
                WiFi
              </>
            ) : (
              <>
                <WifiOff className="w-3 h-3 mr-1" />
                Mobile Data
              </>
            )}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Data Saver Toggle */}
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-white">Data Saver Mode</h4>
            <p className="text-sm text-gray-400">Reduce video quality to save data</p>
          </div>
          <Switch checked={dataSaverMode} onCheckedChange={toggleDataSaver} />
        </div>

        {/* Data Usage Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-gray-800 rounded-lg">
            <BarChart3 className="w-5 h-5 text-blue-400 mx-auto mb-1" />
            <div className="text-lg font-bold text-white">{formatDataUsage(dataUsage.today)}</div>
            <div className="text-xs text-gray-400">Used Today</div>
          </div>
          <div className="text-center p-3 bg-gray-800 rounded-lg">
            <BarChart3 className="w-5 h-5 text-green-400 mx-auto mb-1" />
            <div className="text-lg font-bold text-white">{formatDataUsage(dataUsage.savedToday)}</div>
            <div className="text-xs text-gray-400">Saved Today</div>
          </div>
        </div>

        <div className="text-center p-3 bg-gray-800 rounded-lg">
          <div className="text-xl font-bold text-white">{formatDataUsage(dataUsage.thisMonth)}</div>
          <div className="text-sm text-gray-400">This Month</div>
        </div>

        {/* Data Saver Settings */}
        {dataSaverMode && (
          <div className="space-y-2 p-3 bg-green-900/20 border border-green-700 rounded-lg">
            <h5 className="font-medium text-green-400">Data Saver Active</h5>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Video quality: 480p max</li>
              <li>• Audio-only mode available</li>
              <li>• Reduced thumbnail loading</li>
              <li>• WiFi-only downloads</li>
            </ul>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex space-x-2">
          <Button size="sm" variant="outline" className="flex-1 bg-transparent">
            Audio Only
          </Button>
          <Button size="sm" variant="outline" className="flex-1 bg-transparent">
            240p Quality
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
