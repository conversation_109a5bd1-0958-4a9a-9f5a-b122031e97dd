"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import MobileDataOptimizer from "@/components/mobile-data-optimizer"
import LanguageSelector from "@/components/language-selector"
import LocationDiscovery from "@/components/location-discovery"
import LocalPayments from "@/components/local-payments"
import SACulturalFeatures from "@/components/sa-cultural-features"
import OfflineFeatures from "@/components/offline-features"
import InteractiveMobileFeatures from "@/components/interactive-mobile-features"
import CommunityMonetization from "@/components/community-monetization"
import EnhancedSecurity from "@/components/enhanced-security"

export default function SettingsPage() {
  return (
    <div className="min-h-screen bg-black text-white pb-20">
      {/* Header */}
      <div className="sticky top-0 z-50 bg-black/90 backdrop-blur-sm border-b border-gray-800">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-3">
            <Link href="/profile">
              <ArrowLeft className="w-6 h-6 text-white" />
            </Link>
            <h1 className="text-xl font-bold">Settings</h1>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Mobile Data Optimization */}
        <MobileDataOptimizer />

        {/* Language & Cultural Settings */}
        <LanguageSelector />
        <SACulturalFeatures />

        {/* Location & Discovery */}
        <LocationDiscovery />

        {/* Offline Features */}
        <OfflineFeatures />

        {/* Interactive Mobile Features */}
        <InteractiveMobileFeatures />

        {/* Community & Monetization */}
        <CommunityMonetization />

        {/* Payment Methods */}
        <LocalPayments />

        {/* Enhanced Security */}
        <EnhancedSecurity />

        {/* App Info */}
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">About Aweh.tv</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm text-gray-400">
            <p>Version 1.0.0 - Proudly South African 🇿🇦</p>
            <p>The home of South African live streaming</p>
            <p>Say it. See it. Stream it.</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
