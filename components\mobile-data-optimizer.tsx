"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Wifi, WifiOff, Smartphone, BarChart3, Download, Pause, Volume2, VolumeX } from "lucide-react"

interface DataUsage {
  today: number
  thisMonth: number
  savedToday: number
  limit: number
}

interface ConnectionInfo {
  type: "wifi" | "mobile" | "unknown"
  effectiveType: "slow-2g" | "2g" | "3g" | "4g" | "5g"
  downlink: number
  rtt: number
}

export default function MobileDataOptimizer() {
  const [dataSaverMode, setDataSaverMode] = useState(false)
  const [audioOnlyMode, setAudioOnlyMode] = useState(false)
  const [preloadMode, setPreloadMode] = useState("wifi-only")
  const [videoQuality, setVideoQuality] = useState("auto")
  const [dataUsage, setDataUsage] = useState<DataUsage>({
    today: 45.2,
    thisMonth: 1250.8,
    savedToday: 12.3,
    limit: 2000,
  })
  const [connection, setConnection] = useState<ConnectionInfo>({
    type: "mobile",
    effectiveType: "4g",
    downlink: 2.5,
    rtt: 150,
  })

  useEffect(() => {
    // Check connection type and quality
    if (typeof navigator !== "undefined" && "connection" in navigator) {
      const conn = (navigator as any).connection
      setConnection({
        type: conn?.type === "wifi" ? "wifi" : "mobile",
        effectiveType: conn?.effectiveType || "4g",
        downlink: conn?.downlink || 2.5,
        rtt: conn?.rtt || 150,
      })
    }

    // Load preferences
    const savedPreferences = localStorage.getItem("dataPreferences")
    if (savedPreferences) {
      const prefs = JSON.parse(savedPreferences)
      setDataSaverMode(prefs.dataSaverMode || false)
      setAudioOnlyMode(prefs.audioOnlyMode || false)
      setPreloadMode(prefs.preloadMode || "wifi-only")
      setVideoQuality(prefs.videoQuality || "auto")
    }
  }, [])

  const savePreferences = () => {
    const prefs = {
      dataSaverMode,
      audioOnlyMode,
      preloadMode,
      videoQuality,
    }
    localStorage.setItem("dataPreferences", JSON.stringify(prefs))
  }

  useEffect(() => {
    savePreferences()
  }, [dataSaverMode, audioOnlyMode, preloadMode, videoQuality])

  const formatDataUsage = (mb: number) => {
    if (mb > 1000) {
      return `${(mb / 1000).toFixed(1)} GB`
    }
    return `${mb.toFixed(1)} MB`
  }

  const getConnectionColor = () => {
    if (connection.type === "wifi") return "text-green-400"
    if (connection.effectiveType === "4g" || connection.effectiveType === "5g") return "text-blue-400"
    if (connection.effectiveType === "3g") return "text-yellow-400"
    return "text-red-400"
  }

  const getRecommendedQuality = () => {
    if (connection.type === "wifi") return "1080p"
    if (connection.effectiveType === "4g" || connection.effectiveType === "5g") return "720p"
    if (connection.effectiveType === "3g") return "480p"
    return "240p"
  }

  return (
    <Card className="bg-gray-900 border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-white">
          <Smartphone className="w-5 h-5" />
          <span>Data Optimization</span>
          <Badge variant={connection.type === "wifi" ? "default" : "destructive"} className="ml-2">
            {connection.type === "wifi" ? (
              <>
                <Wifi className="w-3 h-3 mr-1" />
                WiFi
              </>
            ) : (
              <>
                <WifiOff className="w-3 h-3 mr-1" />
                {connection.effectiveType.toUpperCase()}
              </>
            )}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="usage" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-gray-800">
            <TabsTrigger value="usage">Usage</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="quality">Quality</TabsTrigger>
          </TabsList>

          <TabsContent value="usage" className="space-y-4">
            {/* Data Usage Overview */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-800 rounded-lg">
                <BarChart3 className="w-5 h-5 text-blue-400 mx-auto mb-1" />
                <div className="text-lg font-bold text-white">{formatDataUsage(dataUsage.today)}</div>
                <div className="text-xs text-gray-400">Used Today</div>
              </div>
              <div className="text-center p-3 bg-gray-800 rounded-lg">
                <BarChart3 className="w-5 h-5 text-green-400 mx-auto mb-1" />
                <div className="text-lg font-bold text-white">{formatDataUsage(dataUsage.savedToday)}</div>
                <div className="text-xs text-gray-400">Saved Today</div>
              </div>
            </div>

            {/* Monthly Usage Progress */}
            <div className="p-3 bg-gray-800 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-400">Monthly Usage</span>
                <span className="text-sm text-white">
                  {formatDataUsage(dataUsage.thisMonth)} / {formatDataUsage(dataUsage.limit)}
                </span>
              </div>
              <Progress value={(dataUsage.thisMonth / dataUsage.limit) * 100} className="w-full" />
              <div className="text-xs text-gray-500 mt-1">
                {formatDataUsage(dataUsage.limit - dataUsage.thisMonth)} remaining
              </div>
            </div>

            {/* Connection Quality */}
            <div className="p-3 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-2">Connection Quality</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-400">Speed:</span>
                  <span className={`ml-2 font-medium ${getConnectionColor()}`}>{connection.downlink} Mbps</span>
                </div>
                <div>
                  <span className="text-gray-400">Latency:</span>
                  <span className={`ml-2 font-medium ${getConnectionColor()}`}>{connection.rtt}ms</span>
                </div>
              </div>
              <div className="mt-2">
                <span className="text-gray-400 text-sm">Recommended:</span>
                <Badge className="ml-2 bg-green-600">{getRecommendedQuality()}</Badge>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            {/* Data Saver Toggle */}
            <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
              <div>
                <h4 className="font-medium text-white">Data Saver Mode</h4>
                <p className="text-sm text-gray-400">Reduce video quality and preloading</p>
              </div>
              <Switch checked={dataSaverMode} onCheckedChange={setDataSaverMode} />
            </div>

            {/* Audio Only Mode */}
            <div className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
              <div className="flex items-center space-x-2">
                {audioOnlyMode ? (
                  <VolumeX className="w-4 h-4 text-red-400" />
                ) : (
                  <Volume2 className="w-4 h-4 text-blue-400" />
                )}
                <div>
                  <h4 className="font-medium text-white">Audio Only Mode</h4>
                  <p className="text-sm text-gray-400">Listen without video to save data</p>
                </div>
              </div>
              <Switch checked={audioOnlyMode} onCheckedChange={setAudioOnlyMode} />
            </div>

            {/* Preload Settings */}
            <div className="p-3 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Preload Videos</h4>
              <div className="space-y-2">
                {["never", "wifi-only", "always"].map((mode) => (
                  <Button
                    key={mode}
                    variant={preloadMode === mode ? "default" : "outline"}
                    size="sm"
                    onClick={() => setPreloadMode(mode)}
                    className="w-full justify-start"
                  >
                    {mode === "never" && <Pause className="w-4 h-4 mr-2" />}
                    {mode === "wifi-only" && <Wifi className="w-4 h-4 mr-2" />}
                    {mode === "always" && <Download className="w-4 h-4 mr-2" />}
                    {mode === "never" && "Never Preload"}
                    {mode === "wifi-only" && "WiFi Only"}
                    {mode === "always" && "Always Preload"}
                  </Button>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="quality" className="space-y-4">
            {/* Video Quality Settings */}
            <div className="p-3 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-3">Video Quality</h4>
              <div className="space-y-2">
                {["auto", "1080p", "720p", "480p", "240p"].map((quality) => (
                  <Button
                    key={quality}
                    variant={videoQuality === quality ? "default" : "outline"}
                    size="sm"
                    onClick={() => setVideoQuality(quality)}
                    className="w-full justify-between"
                  >
                    <span>{quality === "auto" ? "Auto (Recommended)" : quality}</span>
                    {quality === "auto" && <Badge className="bg-green-600">Smart</Badge>}
                    {quality === "240p" && <Badge className="bg-orange-600">Data Saver</Badge>}
                  </Button>
                ))}
              </div>
            </div>

            {/* Quality Impact */}
            <div className="p-3 bg-gray-800 rounded-lg">
              <h4 className="font-medium text-white mb-2">Data Usage per Hour</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">240p:</span>
                  <span className="text-green-400">~150 MB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">480p:</span>
                  <span className="text-yellow-400">~300 MB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">720p:</span>
                  <span className="text-orange-400">~600 MB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">1080p:</span>
                  <span className="text-red-400">~1.2 GB</span>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
