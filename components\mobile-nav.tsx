"use client"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, Search, Plus, Heart, User } from "lucide-react"

export default function MobileNav() {
  const pathname = usePathname()

  const navItems = [
    { href: "/", icon: Home, label: "Home" },
    { href: "/browse", icon: Search, label: "Browse" },
    { href: "/stream", icon: Plus, label: "Go Live" },
    { href: "/favorites", icon: Heart, label: "Likes" },
    { href: "/profile", icon: User, label: "Profile" },
  ]

  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 bg-black border-t border-gray-800 z-50">
      <div className="flex items-center justify-around py-2">
        {navItems.map((item) => {
          const Icon = item.icon
          const isActive = pathname === item.href

          return (
            <Link
              key={item.href}
              href={item.href}
              className={`flex flex-col items-center py-2 px-3 ${isActive ? "text-green-400" : "text-gray-400"}`}
            >
              <Icon className={`w-6 h-6 ${item.href === "/stream" ? "bg-green-600 p-1 rounded-full" : ""}`} />
              <span className="text-xs mt-1">{item.label}</span>
            </Link>
          )
        })}
      </div>
    </div>
  )
}
