"use client"

import type React from "react"

import { useState } from "react"
import { useChat } from "@ai-sdk/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { MessageCircle, Send, Bot, User } from "lucide-react"

interface StreamChatProps {
  isLive: boolean
}

export default function StreamChat({ isLive }: StreamChatProps) {
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: "/api/chat",
    initialMessages: [
      {
        id: "1",
        role: "assistant",
        content:
          "Welcome to the stream chat! I'm here to help moderate and answer questions. How can I assist you today?",
      },
    ],
    system: `You are an AI chat moderator for Aweh.tv, a South African streaming platform. 
Your role is to:
- Help moderate chat conversations with a friendly SA vibe
- Answer questions about streams and content
- Use South African slang naturally (howzit, lekker, sharp, eish, aweh, boet, my bru, etc.)
- Keep conversations respectful but fun
- Celebrate South African culture and local content
- Help viewers connect with each other
- Be enthusiastic about local creators and content

Keep responses short and conversational for live chat. Use SA slang but don't overdo it.`,
  })

  const [chatMessages, setChatMessages] = useState([
    {
      id: "1",
      user: "StreamMod",
      message: "Howzit everyone! Welcome to the stream! Keep it lekker 🇿🇦",
      isBot: true,
      timestamp: new Date(),
    },
    { id: "2", user: "CapeTownVibes", message: "Eish, this stream is fire! 🔥", isBot: false, timestamp: new Date() },
    { id: "3", user: "JoziGamer", message: "Sharp my bru! Love the content", isBot: false, timestamp: new Date() },
    { id: "4", user: "DurbanShark", message: "Aweh! Greetings from KZN 🌊", isBot: false, timestamp: new Date() },
  ])

  const handleChatSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return

    // Add user message to chat
    const newMessage = {
      id: Date.now().toString(),
      user: "You",
      message: input,
      isBot: false,
      timestamp: new Date(),
    }
    setChatMessages((prev) => [...prev, newMessage])

    // Handle AI chat
    handleSubmit(e)
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2">
          <MessageCircle className="w-5 h-5" />
          <span>Stream Chat</span>
          {isLive && <Badge className="bg-green-500">Live</Badge>}
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        <ScrollArea className="flex-1 px-4">
          <div className="space-y-3">
            {/* Regular Chat Messages */}
            {chatMessages.map((msg) => (
              <div key={msg.id} className="flex items-start space-x-2">
                <div className="flex-shrink-0">
                  {msg.isBot ? (
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <Bot className="w-3 h-3 text-blue-600" />
                    </div>
                  ) : (
                    <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                      <User className="w-3 h-3 text-gray-600" />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">{msg.user}</span>
                    <span className="text-xs text-gray-500">
                      {msg.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 mt-1">{msg.message}</p>
                </div>
              </div>
            ))}

            {/* AI Chat Messages */}
            {messages.map((message) => (
              <div key={message.id} className="flex items-start space-x-2">
                <div className="flex-shrink-0">
                  {message.role === "assistant" ? (
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                      <Bot className="w-3 h-3 text-green-600" />
                    </div>
                  ) : (
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <User className="w-3 h-3 text-blue-600" />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">
                      {message.role === "assistant" ? "AI Moderator" : "You"}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      AI
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-700 mt-1">{message.content}</p>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <Bot className="w-3 h-3 text-green-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">AI Moderator</span>
                    <Badge variant="outline" className="text-xs">
                      AI
                    </Badge>
                  </div>
                  <div className="flex space-x-1 mt-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Chat Input */}
        <div className="border-t p-4">
          <form onSubmit={handleChatSubmit} className="flex space-x-2">
            <Input
              value={input}
              onChange={handleInputChange}
              placeholder={isLive ? "Say something lekker..." : "Chat is disabled when not live"}
              disabled={!isLive || isLoading}
              className="flex-1"
            />
            <Button type="submit" size="sm" disabled={!isLive || !input.trim() || isLoading}>
              <Send className="w-4 h-4" />
            </Button>
          </form>
          <p className="text-xs text-gray-500 mt-2">
            AI moderation is active. Be respectful and follow community guidelines.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
