"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MapPin, Users, Wifi, Navigation } from "lucide-react"

const saProvinces = [
  { code: "WC", name: "Western Cape", city: "Cape Town", emoji: "🏔️" },
  { code: "GP", name: "Gauteng", city: "Johannesburg", emoji: "🏙️" },
  { code: "KZN", name: "KwaZulu-Natal", city: "Durban", emoji: "🌊" },
  { code: "EC", name: "Eastern Cape", city: "Port Elizabeth", emoji: "🦓" },
  { code: "FS", name: "Free State", city: "Bloemfontein", emoji: "🌻" },
  { code: "LP", name: "Limpopo", city: "Polokwane", emoji: "🌳" },
  { code: "MP", name: "Mpumalanga", city: "Nelspruit", emoji: "🦁" },
  { code: "NC", name: "Northern Cape", city: "Kimberley", emoji: "💎" },
  { code: "NW", name: "North West", city: "Mahikeng", emoji: "🌾" },
]

const nearbyStreams = [
  {
    id: 1,
    title: "Braai Tips from Joburg",
    streamer: "BraaiMaster_GP",
    location: "Johannesburg, GP",
    distance: "2.3 km",
    viewers: 234,
    category: "Cooking",
  },
  {
    id: 2,
    title: "Cape Town Sunset Vibes",
    streamer: "CapeTownVibes",
    location: "Cape Town, WC",
    distance: "5.1 km",
    viewers: 456,
    category: "Travel",
  },
  {
    id: 3,
    title: "Durban Beach Walk",
    streamer: "DurbanShark",
    location: "Durban, KZN",
    distance: "12.8 km",
    viewers: 189,
    category: "Lifestyle",
  },
]

export default function LocationDiscovery() {
  const [userLocation, setUserLocation] = useState<string>("")
  const [selectedProvince, setSelectedProvince] = useState<string>("GP")
  const [locationEnabled, setLocationEnabled] = useState(false)

  useEffect(() => {
    // Check if geolocation is available
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocationEnabled(true)
          // In a real app, you'd reverse geocode this to get city/province
          setUserLocation("Johannesburg, Gauteng")
        },
        (error) => {
          console.log("Location access denied")
        },
      )
    }
  }, [])

  const requestLocation = () => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocationEnabled(true)
          setUserLocation("Johannesburg, Gauteng")
        },
        (error) => {
          alert("Location access is needed to show nearby streams")
        },
      )
    }
  }

  return (
    <div className="space-y-6">
      {/* Location Status */}
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <MapPin className="w-5 h-5" />
            <span>Your Location</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {locationEnabled ? (
            <div className="flex items-center space-x-2">
              <Navigation className="w-4 h-4 text-green-400" />
              <span className="text-white">{userLocation}</span>
              <Badge className="bg-green-600">Connected</Badge>
            </div>
          ) : (
            <div className="text-center py-4">
              <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-400 mb-3">Enable location to discover nearby streams</p>
              <Button onClick={requestLocation} className="bg-green-600 hover:bg-green-700">
                <Navigation className="w-4 h-4 mr-2" />
                Enable Location
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Province Selector */}
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">Explore by Province</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-2">
            {saProvinces.map((province) => (
              <Button
                key={province.code}
                variant={selectedProvince === province.code ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedProvince(province.code)}
                className="flex flex-col items-center p-3 h-auto"
              >
                <span className="text-lg mb-1">{province.emoji}</span>
                <span className="text-xs">{province.code}</span>
              </Button>
            ))}
          </div>
          <div className="mt-3 text-center">
            <span className="text-gray-400">Selected: </span>
            <span className="text-white font-medium">{saProvinces.find((p) => p.code === selectedProvince)?.name}</span>
          </div>
        </CardContent>
      </Card>

      {/* Nearby Streams */}
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <Wifi className="w-5 h-5" />
            <span>Streams Near You</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {nearbyStreams.map((stream) => (
            <div key={stream.id} className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg">
              <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-white text-sm">{stream.title}</h4>
                <p className="text-xs text-gray-400">@{stream.streamer}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge variant="outline" className="text-xs border-green-500 text-green-400">
                    {stream.category}
                  </Badge>
                  <span className="text-xs text-gray-500">{stream.distance}</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium text-white">{stream.viewers}</div>
                <div className="text-xs text-gray-400">viewers</div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
}
